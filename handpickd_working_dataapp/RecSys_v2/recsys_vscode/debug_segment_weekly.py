#!/usr/bin/env python3
"""
Debug script to check why segment_weekly_df shows same recommended_items across dates
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

def debug_segment_weekly():
    """Debug the segment_weekly_df creation"""
    
    print("🔍 Debugging segment_weekly_df recommended_items issue")
    print("=" * 60)
    
    try:
        # Check if the main variables exist from demo1.py execution
        if 'recommendations_df' in globals():
            print("✅ Found recommendations_df in globals")
        else:
            print("❌ recommendations_df not found. Running demo1.py first...")
            exec(open('demo1.py').read())
        
        # Now check the recommendations_df
        if 'recommendations_df' in locals() or 'recommendations_df' in globals():
            recs_df = recommendations_df if 'recommendations_df' in locals() else globals()['recommendations_df']
            
            print(f"\n📊 Recommendations DataFrame Info:")
            print(f"   - Shape: {recs_df.shape}")
            print(f"   - Columns: {list(recs_df.columns)}")
            
            if 'recommendation_date' in recs_df.columns:
                print(f"   - Recommendation dates: {recs_df['recommendation_date'].nunique()} unique")
                print(f"   - Date range: {recs_df['recommendation_date'].min()} to {recs_df['recommendation_date'].max()}")
                print(f"   - Date type: {type(recs_df['recommendation_date'].iloc[0])}")
                
                # Show sample of recommendation dates
                date_counts = recs_df['recommendation_date'].value_counts().head()
                print(f"   - Sample date counts:")
                for date, count in date_counts.items():
                    print(f"     {date}: {count} recommendations")
            
            # Check a specific user across different dates
            if 'customer_id' in recs_df.columns:
                sample_user = recs_df['customer_id'].iloc[0]
                user_recs = recs_df[recs_df['customer_id'] == sample_user]
                
                print(f"\n👤 Sample User: {sample_user}")
                print(f"   - Total recommendations: {len(user_recs)}")
                print(f"   - Across dates: {user_recs['recommendation_date'].nunique()}")
                
                # Show recommendations by date
                for date in sorted(user_recs['recommendation_date'].unique()):
                    date_recs = user_recs[user_recs['recommendation_date'] == date]
                    if 'product_name' in date_recs.columns:
                        items = list(date_recs['product_name'].head(5))
                        print(f"   - {date}: {len(date_recs)} items - {items}")
                    else:
                        print(f"   - {date}: {len(date_recs)} items - no product_name column")
        
        # Now check the segment_weekly_df creation logic
        print(f"\n🔍 Testing segment_weekly_df date matching logic:")
        
        if 'customer_orders_rc' in locals() or 'customer_orders_rc' in globals():
            orders_df = customer_orders_rc if 'customer_orders_rc' in locals() else globals()['customer_orders_rc']
            recs_df = recommendations_df if 'recommendations_df' in locals() else globals()['recommendations_df']
            
            # Get the date range used in segment_weekly_df
            max_date = pd.to_datetime(orders_df['delivery_date']).max()
            min_date = max_date - pd.Timedelta(days=6)
            
            print(f"   - Date range: {min_date} to {max_date}")
            
            # Test with a sample user and date
            sample_user = recs_df['customer_id'].iloc[0]
            test_day = max_date  # Use the last day
            
            print(f"   - Testing user {sample_user} on {test_day}")
            
            # Test the exact filtering logic from segment_weekly_df
            recs_filtered = recs_df[
                (recs_df['customer_id'] == sample_user) &
                (pd.to_datetime(recs_df['recommendation_date']) == test_day)
            ]
            
            print(f"   - Filtered recommendations: {len(recs_filtered)}")
            
            if len(recs_filtered) > 0:
                if 'product_name' in recs_filtered.columns:
                    items = list(recs_filtered['product_name'])
                    print(f"   - Items: {items[:5]}")
                else:
                    print(f"   - No product_name column in filtered results")
                    print(f"   - Available columns: {list(recs_filtered.columns)}")
            else:
                print(f"   - No recommendations found for this user/date combination")
                
                # Debug why no matches
                user_recs = recs_df[recs_df['customer_id'] == sample_user]
                print(f"   - User has {len(user_recs)} total recommendations")
                
                if len(user_recs) > 0:
                    print(f"   - User's recommendation dates: {sorted(user_recs['recommendation_date'].unique())}")
                    print(f"   - Test day: {test_day}")
                    print(f"   - Test day type: {type(test_day)}")
                    print(f"   - Rec date type: {type(user_recs['recommendation_date'].iloc[0])}")
                    
                    # Try different date comparisons
                    for rec_date in user_recs['recommendation_date'].unique()[:3]:
                        print(f"   - {rec_date} == {test_day}: {rec_date == test_day}")
                        print(f"   - pd.to_datetime({rec_date}) == {test_day}: {pd.to_datetime(rec_date) == test_day}")
        
        else:
            print("❌ customer_orders_rc not found")
            
    except Exception as e:
        print(f"❌ Error during debugging: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_segment_weekly()
