#!/usr/bin/env python3
"""
Test script to verify that the caching fix works correctly.
This script checks if different evaluation dates produce different recommendations.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the current directory to Python path to import demo1
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_caching_fix():
    """Test that different evaluation dates produce different cache hashes"""

    print("🧪 Testing Caching Fix for Recommendation System")
    print("=" * 60)

    try:
        # Import the recommendation system from demo1
        from demo1 import UltraOptimizedRecommendationSystem

        # Create a simple test dataset
        print("📊 Creating test dataset...")

        # Create sample data
        dates = pd.date_range('2025-07-01', '2025-07-10', freq='D')
        customers = [f'customer_{i}' for i in range(1, 6)]
        products = [f'product_{i}' for i in range(1, 11)]

        # Generate sample orders
        orders_data = []
        for date in dates:
            for customer in customers:
                # Each customer buys 2-4 random products each day
                num_products = np.random.randint(2, 5)
                selected_products = np.random.choice(products, num_products, replace=False)
                for product in selected_products:
                    orders_data.append({
                        'customer_id': customer,
                        'sku_code': product,
                        'delivery_date': date,
                        'ordered_qty': np.random.randint(1, 4),
                        'display_order_id': f'order_{date.strftime("%Y%m%d")}_{customer}'
                    })

        customer_orders_rc = pd.DataFrame(orders_data)

        # Create sample catalogue
        catalogue_data = []
        for product in products:
            catalogue_data.append({
                'sku_code': product,
                'name': product.replace('_', ' ').title(),
                'category_name': f'Category {product[-1]}',
                'sku_parent_name': f'Parent {product[-1]}'
            })

        catalogue_rc = pd.DataFrame(catalogue_data)

        # Create sample repurchase ratios
        repurchase_data = []
        for customer in customers:
            repurchase_data.append({
                'customer_id': customer,
                'repurchase_ratio': np.random.uniform(0.5, 0.9)
            })

        repurchase_ratios_df = pd.DataFrame(repurchase_data)
        
        print(f"📊 Created test data:")
        print(f"   - Catalogue: {len(catalogue_rc)} items")
        print(f"   - Orders: {len(customer_orders_rc)} orders")
        print(f"   - Repurchase ratios: {len(repurchase_ratios_df)} users")

        # Initialize the recommendation system
        rec_system = UltraOptimizedRecommendationSystem(
            user_repurchase_ratios_df=repurchase_ratios_df,
            product_name_column='name'
        )

        # Precompute static features
        rec_system.precompute_static_features(customer_orders_rc, catalogue_rc)

        # Convert delivery_date to datetime if not already
        if not pd.api.types.is_datetime64_any_dtype(customer_orders_rc['delivery_date']):
            customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])

        # Get the last few dates for testing
        max_date = customer_orders_rc['delivery_date'].max()
        test_dates = [
            max_date - timedelta(days=2),
            max_date - timedelta(days=1),
            max_date
        ]
        
        print(f"\n🗓️ Testing with evaluation dates:")
        for i, date in enumerate(test_dates):
            print(f"   {i+1}. {date.strftime('%Y-%m-%d')}")
        
        # Test cache hash generation first
        print(f"\n🔍 Testing Cache Hash Generation:")
        print("=" * 40)

        cache_hashes = {}
        for eval_date in test_dates:
            train_data = customer_orders_rc[customer_orders_rc['delivery_date'] < eval_date]
            if len(train_data) > 0:
                # Convert to product names for hash calculation
                orders_with_names = train_data.copy()
                orders_with_names['product_name'] = orders_with_names['sku_code'].map(rec_system.sku_to_name_mapping)
                orders_with_names = orders_with_names.dropna(subset=['product_name'])

                # Generate hash (same logic as in build_matrices_ultra_fast)
                import hashlib
                eval_date_str = eval_date.strftime('%Y-%m-%d')
                data_hash = hashlib.md5(f"{len(orders_with_names)}_{orders_with_names['delivery_date'].min()}_{orders_with_names['delivery_date'].max()}_{rec_system.product_name_column}_{eval_date_str}".encode()).hexdigest()

                cache_hashes[eval_date] = data_hash
                print(f"   {eval_date.strftime('%Y-%m-%d')}: {data_hash[:12]}... (train orders: {len(train_data)})")

        # Check if hashes are different
        unique_hashes = set(cache_hashes.values())
        print(f"\n📊 Cache Hash Analysis:")
        print(f"   - Total dates tested: {len(cache_hashes)}")
        print(f"   - Unique hashes: {len(unique_hashes)}")

        if len(unique_hashes) == len(cache_hashes):
            print(f"   ✅ SUCCESS: All dates have unique cache hashes!")
            print(f"   🔧 The caching fix is working correctly.")
        else:
            print(f"   ⚠️ WARNING: Some dates have identical cache hashes!")
            print(f"   🔧 The caching issue may still exist.")

        # Test with a sample user
        sample_users = customer_orders_rc['customer_id'].value_counts().head(3).index.tolist()
        test_user = sample_users[0]
        print(f"\n👤 Testing recommendations with user: {test_user}")

        # Store recommendations for each date
        recommendations_by_date = {}

        for eval_date in test_dates:
            print(f"\n📅 Generating recommendations for {eval_date.strftime('%Y-%m-%d')}...")
            
            # Filter training data (same logic as in demo1.py)
            train_data = customer_orders_rc[customer_orders_rc['delivery_date'] < eval_date]
            
            if len(train_data) == 0:
                print(f"   ⚠️ No training data for {eval_date}")
                continue
            
            print(f"   📊 Training data: {len(train_data)} orders")
            
            # Build matrices (this should use different cache for each eval_date now)
            model_data = rec_system.build_matrices_ultra_fast(train_data, eval_date)
            user_profiles = rec_system.build_user_profiles_lightning_fast(
                model_data['user_item_matrix'], 
                model_data['user_item_qty_matrix']
            )
            
            # Generate recommendations for the test user
            if test_user in user_profiles:
                recommendations = rec_system.get_recommendations_high_recall_ensemble(
                    test_user, user_profiles[test_user], model_data['similarity_dict'], 10
                )
                
                # Extract product names
                recommended_items = [rec['product_name'] for rec in recommendations]
                recommendations_by_date[eval_date] = recommended_items
                
                print(f"   ✅ Generated {len(recommended_items)} recommendations")
                print(f"   📝 Sample items: {recommended_items[:3]}")
            else:
                print(f"   ⚠️ User {test_user} not found in user profiles")
        
        # Compare recommendations across dates
        print(f"\n🔍 Comparing Recommendations Across Dates:")
        print("=" * 50)
        
        dates = list(recommendations_by_date.keys())
        if len(dates) >= 2:
            for i in range(len(dates)):
                for j in range(i+1, len(dates)):
                    date1, date2 = dates[i], dates[j]
                    recs1 = set(recommendations_by_date[date1])
                    recs2 = set(recommendations_by_date[date2])
                    
                    intersection = recs1.intersection(recs2)
                    union = recs1.union(recs2)
                    
                    similarity = len(intersection) / len(union) if len(union) > 0 else 0
                    
                    print(f"📊 {date1.strftime('%Y-%m-%d')} vs {date2.strftime('%Y-%m-%d')}:")
                    print(f"   - Common items: {len(intersection)}/{len(union)} ({similarity:.1%})")
                    print(f"   - Unique to {date1.strftime('%m-%d')}: {len(recs1 - recs2)}")
                    print(f"   - Unique to {date2.strftime('%m-%d')}: {len(recs2 - recs1)}")
                    
                    if similarity == 1.0:
                        print(f"   ⚠️ WARNING: Identical recommendations detected!")
                        print(f"   🔧 This suggests the caching fix may not be working properly.")
                    else:
                        print(f"   ✅ Different recommendations detected - caching fix working!")
                    print()
        
        # Summary
        print("🎯 Test Summary:")
        if len(recommendations_by_date) >= 2:
            all_similarities = []
            dates = list(recommendations_by_date.keys())
            for i in range(len(dates)):
                for j in range(i+1, len(dates)):
                    recs1 = set(recommendations_by_date[dates[i]])
                    recs2 = set(recommendations_by_date[dates[j]])
                    union = recs1.union(recs2)
                    similarity = len(recs1.intersection(recs2)) / len(union) if len(union) > 0 else 0
                    all_similarities.append(similarity)
            
            avg_similarity = np.mean(all_similarities)
            print(f"   - Average similarity between dates: {avg_similarity:.1%}")
            
            if avg_similarity < 0.8:
                print(f"   ✅ SUCCESS: Recommendations vary across dates (avg similarity: {avg_similarity:.1%})")
                print(f"   🔧 The caching fix appears to be working correctly!")
            else:
                print(f"   ⚠️ CONCERN: High similarity across dates (avg similarity: {avg_similarity:.1%})")
                print(f"   🔧 The caching issue may still exist.")
        else:
            print(f"   ⚠️ Insufficient data to compare recommendations across dates")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_caching_fix()
