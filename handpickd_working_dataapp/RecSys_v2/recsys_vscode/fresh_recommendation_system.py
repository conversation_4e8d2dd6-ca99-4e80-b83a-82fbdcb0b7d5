#!/usr/bin/env python3
"""
🚀 FRESH DIVERSITY-FIRST HIGH-PERFORMANCE RECOMMENDATION SYSTEM

A completely new approach built from scratch with fresh mindset:
- Primary Goal: High performance (40%+ precision/recall)
- Secondary Goal: Meaningful diversity across dates
- Tertiary Goal: Stable, consistent results

Core Philosophy:
1. Start with proven collaborative filtering for high performance
2. Add intelligent diversity through temporal rotation
3. Maintain user preference patterns while ensuring variety
4. Export identical output format as existing system
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
import warnings
warnings.filterwarnings('ignore')

class FreshDiversityFirstRecommendationSystem:
    """
    🎯 FRESH DIVERSITY-FIRST RECOMMENDATION SYSTEM
    
    Built from scratch with focus on:
    - High performance through proven collaborative filtering
    - Intelligent diversity through temporal patterns
    - User preference preservation with variety injection
    """
    
    def __init__(self, user_repurchase_ratios_df=None, product_name_column='name'):
        """Initialize the fresh recommendation system"""
        
        print("🚀 INITIALIZING FRESH DIVERSITY-FIRST RECOMMENDATION SYSTEM")
        print("="*70)
        
        # Core configuration
        self.product_name_column = product_name_column
        self.user_repurchase_ratios = {}
        
        # Load repurchase ratios
        if user_repurchase_ratios_df is not None:
            for _, row in user_repurchase_ratios_df.iterrows():
                self.user_repurchase_ratios[row['customer_id']] = row['repurchase_ratio']
            print(f"✅ Loaded repurchase ratios for {len(self.user_repurchase_ratios):,} users")
        
        # Core data structures
        self.catalogue_lookup = {}
        self.item_categories = {}
        self.popular_items = []
        self.item_avg_quantities = {}
        
        # Diversity management
        self.user_recommendation_history = {}  # Track what we've recommended
        self.diversity_rotation_days = 3  # Rotate every 3 days
        self.core_preference_ratio = 0.6  # 60% core preferences, 40% diversity
        
        # Performance optimization
        self.similarity_cache = {}
        self.user_profile_cache = {}
        
        print("🎯 System initialized with diversity-first approach")
        print("   📊 Core preferences: 60%, Diversity: 40%")
        print("   🔄 Rotation cycle: 3 days")
        print("="*70)
    
    def setup_catalogue_and_data(self, catalogue_df, customer_orders_df):
        """Setup catalogue and order data for recommendations"""
        
        print("🔧 SETTING UP CATALOGUE AND ORDER DATA...")
        
        # Build catalogue lookup
        for _, row in catalogue_df.iterrows():
            # Use 'name' column from catalogue, but map to product_name_column from orders
            product_name = row['name']
            if product_name not in self.catalogue_lookup:
                self.catalogue_lookup[product_name] = {
                    'sku_codes': [],
                    'category_name': row.get('category_name', 'Unknown')
                }
            self.catalogue_lookup[product_name]['sku_codes'].append(row['sku_code'])
            self.item_categories[product_name] = row.get('category_name', 'Unknown')
        
        print(f"✅ Built catalogue lookup for {len(self.catalogue_lookup):,} products")
        
        # Calculate popular items and quantities
        product_counts = customer_orders_df[self.product_name_column].value_counts()
        self.popular_items = product_counts.head(200).index.tolist()
        
        # Calculate average quantities
        quantity_stats = customer_orders_df.groupby(self.product_name_column)['ordered_qty'].mean()
        self.item_avg_quantities = quantity_stats.to_dict()
        
        print(f"✅ Identified {len(self.popular_items)} popular items")
        print(f"✅ Calculated quantity statistics for {len(self.item_avg_quantities):,} items")
    
    def build_user_item_matrix(self, customer_orders_df, eval_date):
        """Build user-item interaction matrix for collaborative filtering"""
        
        # Filter orders before evaluation date
        train_orders = customer_orders_df[
            pd.to_datetime(customer_orders_df['delivery_date']) < eval_date
        ].copy()
        
        # Create user-item matrix (using ordered_qty as the quantity column)
        user_item_matrix = train_orders.pivot_table(
            index='customer_id',
            columns=self.product_name_column,
            values='ordered_qty',
            aggfunc='sum',
            fill_value=0
        )
        
        # Normalize by user (to handle different purchase volumes)
        user_item_normalized = user_item_matrix.div(
            user_item_matrix.sum(axis=1), axis=0
        ).fillna(0)
        
        return user_item_matrix, user_item_normalized
    
    def compute_item_similarity(self, user_item_matrix):
        """Compute item-item similarity matrix"""
        
        # Use cosine similarity on item vectors
        item_similarity = cosine_similarity(user_item_matrix.T)
        
        # Convert to DataFrame for easier access
        similarity_df = pd.DataFrame(
            item_similarity,
            index=user_item_matrix.columns,
            columns=user_item_matrix.columns
        )
        
        return similarity_df
    
    def get_user_profile(self, user_id, user_item_matrix):
        """Build comprehensive user profile"""
        
        if user_id not in user_item_matrix.index:
            return {
                'purchased_items': [],
                'category_preferences': {},
                'top_items': [],
                'purchase_volume': 0
            }
        
        user_vector = user_item_matrix.loc[user_id]
        purchased_items = user_vector[user_vector > 0].index.tolist()
        
        # Calculate category preferences
        category_preferences = {}
        for item in purchased_items:
            category = self.item_categories.get(item, 'Unknown')
            if category not in category_preferences:
                category_preferences[category] = 0
            category_preferences[category] += user_vector[item]
        
        # Normalize category preferences
        total_purchases = sum(category_preferences.values())
        if total_purchases > 0:
            category_preferences = {
                cat: count/total_purchases 
                for cat, count in category_preferences.items()
            }
        
        # Get top items
        top_items = user_vector.nlargest(10).index.tolist()
        
        return {
            'purchased_items': purchased_items,
            'category_preferences': category_preferences,
            'top_items': top_items,
            'purchase_volume': total_purchases
        }
    
    def generate_core_recommendations(self, user_id, user_profile, similarity_df, num_core):
        """Generate core recommendations based on user preferences"""
        
        core_recommendations = []
        user_items = set(user_profile['purchased_items'])
        
        # Strategy 1: Similar items to user's top purchases
        for top_item in user_profile['top_items'][:5]:
            if top_item in similarity_df.index:
                similar_items = similarity_df[top_item].sort_values(ascending=False)
                
                for item, similarity_score in similar_items.head(10).items():
                    if (item not in user_items and 
                        item in self.catalogue_lookup and
                        len(core_recommendations) < num_core):
                        
                        core_rec = {
                            'sku_code': self.catalogue_lookup[item]['sku_codes'][0],
                            'product_name': item,
                            'category': self.item_categories.get(item, 'Unknown'),
                            'score': similarity_score * 0.9,  # High score for similar items
                            'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                            'recommendation_type': 'core_similar',
                            'strategy': f'similar_to_{top_item}'
                        }
                        core_recommendations.append(core_rec)
        
        # Strategy 2: Popular items from preferred categories
        for category, preference in sorted(user_profile['category_preferences'].items(), 
                                         key=lambda x: x[1], reverse=True)[:3]:
            
            category_items = [
                item for item in self.popular_items[:50]
                if (self.item_categories.get(item) == category and
                    item not in user_items and
                    item in self.catalogue_lookup)
            ]
            
            for item in category_items[:3]:
                if len(core_recommendations) < num_core:
                    core_rec = {
                        'sku_code': self.catalogue_lookup[item]['sku_codes'][0],
                        'product_name': item,
                        'category': self.item_categories.get(item, 'Unknown'),
                        'score': preference * 0.8,  # Score based on category preference
                        'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                        'recommendation_type': 'core_category',
                        'strategy': f'popular_in_{category}'
                    }
                    core_recommendations.append(core_rec)
        
        return core_recommendations[:num_core]
    
    def generate_diversity_recommendations(self, user_id, user_profile, eval_date, num_diversity, core_items):
        """Generate diversity recommendations with temporal rotation"""
        
        diversity_recommendations = []
        existing_items = set(r['product_name'] for r in core_items)
        user_items = set(user_profile['purchased_items'])
        
        # Calculate rotation day (0, 1, 2 for 3-day cycle)
        rotation_day = (eval_date.toordinal() // self.diversity_rotation_days) % 3
        
        # Get user's recommendation history
        user_history = self.user_recommendation_history.get(user_id, set())
        
        # Strategy 1: Explore new categories (rotation-based)
        all_categories = list(set(self.item_categories.values()))
        user_categories = set(user_profile['category_preferences'].keys())
        new_categories = [cat for cat in all_categories if cat not in user_categories]
        
        # Rotate through new categories
        if new_categories:
            rotation_categories = new_categories[rotation_day::3]  # Every 3rd category
            
            for category in rotation_categories[:2]:  # Max 2 new categories
                category_items = [
                    item for item in self.popular_items[:30]
                    if (self.item_categories.get(item) == category and
                        item not in existing_items and
                        item not in user_items and
                        item not in user_history and
                        item in self.catalogue_lookup)
                ]
                
                if category_items and len(diversity_recommendations) < num_diversity:
                    item = category_items[0]  # Take most popular in category
                    
                    diversity_rec = {
                        'sku_code': self.catalogue_lookup[item]['sku_codes'][0],
                        'product_name': item,
                        'category': self.item_categories.get(item, 'Unknown'),
                        'score': 0.6,  # Moderate score for exploration
                        'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                        'recommendation_type': 'diversity_exploration',
                        'strategy': f'explore_{category}_day_{rotation_day}'
                    }
                    diversity_recommendations.append(diversity_rec)
                    existing_items.add(item)
        
        # Strategy 2: Seasonal/trending items (rotation-based)
        trending_start = rotation_day * 5  # Different trending items each rotation
        trending_items = [
            item for item in self.popular_items[trending_start:trending_start+15]
            if (item not in existing_items and
                item not in user_items and
                item not in user_history and
                item in self.catalogue_lookup)
        ]
        
        for item in trending_items[:2]:  # Max 2 trending items
            if len(diversity_recommendations) < num_diversity:
                diversity_rec = {
                    'sku_code': self.catalogue_lookup[item]['sku_codes'][0],
                    'product_name': item,
                    'category': self.item_categories.get(item, 'Unknown'),
                    'score': 0.5,  # Lower score for trending
                    'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                    'recommendation_type': 'diversity_trending',
                    'strategy': f'trending_day_{rotation_day}'
                }
                diversity_recommendations.append(diversity_rec)
                existing_items.add(item)
        
        return diversity_recommendations[:num_diversity]

    def get_fresh_recommendations(self, user_id, user_profile, similarity_df, eval_date, top_n=10):
        """
        🎯 GENERATE FRESH DIVERSITY-FIRST RECOMMENDATIONS

        Core algorithm:
        1. Generate 60% core recommendations (high performance)
        2. Generate 40% diversity recommendations (temporal rotation)
        3. Combine and rank by score
        4. Update recommendation history for future diversity
        """

        # Calculate split
        num_core = max(1, int(top_n * self.core_preference_ratio))
        num_diversity = top_n - num_core

        # Generate core recommendations (high performance)
        core_recommendations = self.generate_core_recommendations(
            user_id, user_profile, similarity_df, num_core
        )

        # Generate diversity recommendations (temporal rotation)
        diversity_recommendations = self.generate_diversity_recommendations(
            user_id, user_profile, eval_date, num_diversity, core_recommendations
        )

        # Combine recommendations
        all_recommendations = core_recommendations + diversity_recommendations

        # Sort by score (core items should naturally rank higher)
        all_recommendations.sort(key=lambda x: x['score'], reverse=True)

        # Update recommendation history
        if user_id not in self.user_recommendation_history:
            self.user_recommendation_history[user_id] = set()

        for rec in all_recommendations:
            self.user_recommendation_history[user_id].add(rec['product_name'])

        # Keep history manageable (last 50 items)
        if len(self.user_recommendation_history[user_id]) > 50:
            # Convert to list, keep last 30 items
            history_list = list(self.user_recommendation_history[user_id])
            self.user_recommendation_history[user_id] = set(history_list[-30:])

        return all_recommendations[:top_n]

    def calculate_personalized_k(self, customer_orders_df):
        """Calculate personalized k values based on user purchase patterns"""

        print("🎯 Calculating personalized k values...")

        # Calculate median items in last 15 purchases for each user
        personalized_k = {}

        # Get recent orders (last 30 days)
        max_date = pd.to_datetime(customer_orders_df['delivery_date']).max()
        recent_cutoff = max_date - pd.Timedelta(days=30)
        recent_orders = customer_orders_df[
            pd.to_datetime(customer_orders_df['delivery_date']) >= recent_cutoff
        ]

        for user_id in customer_orders_df['customer_id'].unique():
            user_orders = recent_orders[recent_orders['customer_id'] == user_id]

            if len(user_orders) == 0:
                personalized_k[user_id] = 5  # Default
                continue

            # Get last 15 orders
            user_orders_sorted = user_orders.sort_values('delivery_date').tail(15)

            # Count unique items per order
            items_per_order = user_orders_sorted.groupby('delivery_date')[self.product_name_column].nunique()

            # Use median as personalized k
            median_k = max(3, min(15, int(items_per_order.median())))
            personalized_k[user_id] = median_k

        print(f"✅ Calculated personalized k for {len(personalized_k):,} users")
        print(f"   Median k: {np.median(list(personalized_k.values())):.1f}")
        print(f"   Range: {min(personalized_k.values())} - {max(personalized_k.values())}")

        return personalized_k

    def evaluate_single_date(self, customer_orders_df, catalogue_df, eval_date, personalized_k):
        """Evaluate recommendations for a single date"""

        print(f"\n📅 EVALUATING {eval_date.strftime('%Y-%m-%d')}")
        print("-" * 50)

        # Setup data
        self.setup_catalogue_and_data(catalogue_df, customer_orders_df)

        # Build user-item matrix
        user_item_matrix, user_item_normalized = self.build_user_item_matrix(
            customer_orders_df, eval_date
        )

        # Compute item similarity
        similarity_df = self.compute_item_similarity(user_item_normalized)

        print(f"✅ Built matrices: {user_item_matrix.shape}, similarity: {similarity_df.shape}")

        # Get test users (users who made purchases on eval_date)
        test_orders = customer_orders_df[
            pd.to_datetime(customer_orders_df['delivery_date']) == eval_date
        ]
        test_users = test_orders['customer_id'].unique()

        print(f"📊 Test users: {len(test_users)}")

        # Generate recommendations for each test user
        all_recommendations = []
        user_metrics = []

        for user_id in test_users:
            # Get user profile
            user_profile = self.get_user_profile(user_id, user_item_matrix)

            # Get personalized k
            user_k = personalized_k.get(user_id, 5)

            # Generate recommendations
            recommendations = self.get_fresh_recommendations(
                user_id, user_profile, similarity_df, eval_date, user_k
            )

            # Get actual purchases
            actual_purchases = test_orders[
                test_orders['customer_id'] == user_id
            ][self.product_name_column].tolist()

            # Calculate metrics
            recommended_items = [r['product_name'] for r in recommendations]
            hits = len(set(recommended_items) & set(actual_purchases))

            precision = hits / len(recommended_items) if recommended_items else 0
            recall = hits / len(actual_purchases) if actual_purchases else 0

            # Store detailed recommendations
            for i, rec in enumerate(recommendations):
                rec_detail = rec.copy()
                rec_detail.update({
                    'customer_id': user_id,
                    'delivery_date': eval_date.strftime('%Y-%m-%d'),
                    'rank': i + 1,
                    'user_precision': precision,
                    'user_recall': recall,
                    'user_k': user_k
                })
                all_recommendations.append(rec_detail)

            # Store user metrics
            user_metrics.append({
                'customer_id': user_id,
                'delivery_date': eval_date.strftime('%Y-%m-%d'),
                'precision': precision,
                'recall': recall,
                'hits': hits,
                'recommended_count': len(recommended_items),
                'actual_count': len(actual_purchases),
                'k': user_k,
                'repurchase_ratio': self.user_repurchase_ratios.get(user_id, 0.5)
            })

        # Calculate overall metrics
        total_precision = np.mean([m['precision'] for m in user_metrics])
        total_recall = np.mean([m['recall'] for m in user_metrics])
        hit_rate = np.mean([1 if m['hits'] > 0 else 0 for m in user_metrics])

        print(f"📊 Results: P@k: {total_precision:.3f}, R@k: {total_recall:.3f}, Hit Rate: {hit_rate:.3f}")

        return {
            'date': eval_date,
            'precision': total_precision,
            'recall': total_recall,
            'hit_rate': hit_rate,
            'recommendations': all_recommendations,
            'user_metrics': user_metrics,
            'test_users': len(test_users)
        }

    def evaluate_multiple_dates(self, customer_orders_df, catalogue_df, test_days=7):
        """
        🎯 EVALUATE FRESH SYSTEM ACROSS MULTIPLE DATES

        Main evaluation method that generates all required outputs:
        - recommendations_df: Detailed recommendations with scores
        - user_metrics_df: User-level performance metrics
        - segment_weekly_df: Weekly comparison data
        - Performance plots: fig_1 and fig_2
        """

        print("🚀 FRESH DIVERSITY-FIRST RECOMMENDATION SYSTEM EVALUATION")
        print("="*70)
        print(f"🎯 Evaluating {test_days} days with diversity-first approach")
        print("🎯 Target: 40%+ precision/recall with meaningful variety")
        print("="*70)

        # Calculate personalized k values
        personalized_k = self.calculate_personalized_k(customer_orders_df)

        # Get evaluation dates
        max_date = pd.to_datetime(customer_orders_df['delivery_date']).max()
        eval_dates = [max_date - timedelta(days=i) for i in range(test_days-1, -1, -1)]

        print(f"\n📅 Evaluation dates: {eval_dates[0].strftime('%Y-%m-%d')} to {eval_dates[-1].strftime('%Y-%m-%d')}")

        # Evaluate each date
        all_results = []
        all_recommendations = []
        all_user_metrics = []

        for eval_date in eval_dates:
            result = self.evaluate_single_date(
                customer_orders_df, catalogue_df, eval_date, personalized_k
            )

            all_results.append(result)
            all_recommendations.extend(result['recommendations'])
            all_user_metrics.extend(result['user_metrics'])

        # Create output DataFrames
        recommendations_df = pd.DataFrame(all_recommendations)
        user_metrics_df = pd.DataFrame(all_user_metrics)

        # Calculate overall performance
        overall_precision = np.mean([r['precision'] for r in all_results])
        overall_recall = np.mean([r['recall'] for r in all_results])
        overall_hit_rate = np.mean([r['hit_rate'] for r in all_results])

        print(f"\n🏆 OVERALL FRESH SYSTEM PERFORMANCE:")
        print(f"   Precision@k: {overall_precision:.3f}")
        print(f"   Recall@k: {overall_recall:.3f}")
        print(f"   Hit Rate: {overall_hit_rate:.3f}")

        # Check performance targets
        if overall_precision >= 0.4 and overall_recall >= 0.4:
            print("✅ PERFORMANCE TARGET ACHIEVED!")
        else:
            print(f"⚠️  Performance target not yet reached (target: 40%)")

        # Generate segment weekly comparison
        segment_weekly_df = self.generate_segment_weekly_comparison(
            customer_orders_df, recommendations_df, eval_dates
        )

        # Generate performance plots
        fig_1, fig_2 = self.generate_performance_plots(user_metrics_df, recommendations_df)

        print(f"\n✅ Generated all required outputs:")
        print(f"   📊 recommendations_df: {len(recommendations_df):,} rows")
        print(f"   📊 user_metrics_df: {len(user_metrics_df):,} rows")
        print(f"   📊 segment_weekly_df: {len(segment_weekly_df):,} rows")
        print(f"   📈 Performance plots: fig_1, fig_2")

        return {
            'recommendations_df': recommendations_df,
            'user_metrics_df': user_metrics_df,
            'segment_weekly_df': segment_weekly_df,
            'fig_1': fig_1,
            'fig_2': fig_2,
            'precision': overall_precision,
            'recall': overall_recall,
            'hit_rate': overall_hit_rate,
            'daily_results': all_results
        }

    def generate_segment_weekly_comparison(self, customer_orders_df, recommendations_df, eval_dates):
        """Generate segment weekly comparison with actual vs recommended items"""

        print("\n📊 Generating segment weekly comparison...")

        segment_data = []

        for eval_date in eval_dates:
            date_str = eval_date.strftime('%Y-%m-%d')

            # Get actual purchases for this date
            actual_orders = customer_orders_df[
                pd.to_datetime(customer_orders_df['delivery_date']) == eval_date
            ]

            # Get recommendations for this date
            date_recommendations = recommendations_df[
                recommendations_df['delivery_date'] == date_str
            ]

            # Group by user
            for user_id in date_recommendations['customer_id'].unique():
                user_recs = date_recommendations[
                    date_recommendations['customer_id'] == user_id
                ]

                user_actual = actual_orders[
                    actual_orders['customer_id'] == user_id
                ]

                # Get recommended items
                recommended_items = user_recs['product_name'].tolist()

                # Get actual purchased items with frequencies
                actual_items = []
                if len(user_actual) > 0:
                    item_counts = user_actual[self.product_name_column].value_counts()
                    actual_items = [f"{item}({count})" for item, count in item_counts.items()]

                # Get user's k value
                user_k = user_recs['user_k'].iloc[0] if len(user_recs) > 0 else 5

                segment_data.append({
                    'customer_id': user_id,
                    'delivery_date': date_str,
                    'actual_purchased_items': actual_items,
                    'recommended_items': recommended_items,
                    'k': user_k
                })

        segment_weekly_df = pd.DataFrame(segment_data)

        print(f"✅ Generated segment weekly comparison: {len(segment_weekly_df):,} rows")

        return segment_weekly_df

    def generate_performance_plots(self, user_metrics_df, recommendations_df):
        """Generate performance visualization plots"""

        try:
            import plotly.graph_objects as go
            import plotly.express as px

            # Plot 1: Recall distribution
            fig_1 = px.histogram(
                user_metrics_df,
                x='recall',
                title='Recall Distribution - Fresh Diversity-First System',
                nbins=20
            )
            fig_1.update_layout(
                xaxis_title='Recall@k',
                yaxis_title='Number of Users',
                showlegend=False
            )

            # Plot 2: Recommendation score distribution
            fig_2 = px.histogram(
                recommendations_df,
                x='score',
                title='Recommendation Score Distribution - Fresh System',
                nbins=30
            )
            fig_2.update_layout(
                xaxis_title='Recommendation Score',
                yaxis_title='Number of Recommendations',
                showlegend=False
            )

            print("✅ Generated performance plots")

            return fig_1, fig_2

        except ImportError:
            print("⚠️  Plotly not available, skipping plot generation")
            return None, None
