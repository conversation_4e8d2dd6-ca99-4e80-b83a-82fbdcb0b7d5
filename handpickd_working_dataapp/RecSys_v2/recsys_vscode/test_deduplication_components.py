#!/usr/bin/env python3
"""
Test individual components of the Intelligent Recommendation Deduplication System
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

def test_deduplication_components():
    """Test individual components of the deduplication system"""
    
    print("🧪 Testing Deduplication System Components")
    print("=" * 50)
    
    try:
        # Import the recommendation system
        from demo1 import UltraOptimizedRecommendationSystem
        
        # Create a simple test system
        rec_system = UltraOptimizedRecommendationSystem(product_name_column='name')
        
        # Create sample data
        print("📊 Creating test data...")
        
        # Sample products
        products = ['Milk', 'Bread', 'Eggs', 'Banana', 'Apple', 'Tomato', 'Onion', 'Potato']
        categories = {'Milk': 'Dairy', 'Bread': 'Bakery', 'Eggs': 'Dairy', 'Banana': 'Fruit', 
                     'Apple': 'Fruit', 'Tomato': 'Vegetable', 'Onion': 'Vegetable', 'Potato': 'Vegetable'}
        
        # Set up basic mappings
        rec_system.sku_to_name_mapping = {p: p for p in products}
        rec_system.name_to_sku_mapping = {p: [p] for p in products}
        rec_system.item_categories = categories
        rec_system.item_purchase_frequency = {p: np.random.uniform(0.1, 0.5) for p in products}
        rec_system.item_avg_quantities = {p: np.random.uniform(1.0, 3.0) for p in products}
        rec_system.popular_items = products
        rec_system.catalogue_lookup = {
            p: {'sku_codes': [p], 'category_name': categories[p]} for p in products
        }
        
        # Create sample order data for daily pattern analysis
        dates = pd.date_range(start='2025-06-20', end='2025-07-10', freq='D')
        orders_data = []
        
        # Create realistic purchase patterns
        test_user = 'test_user_1'
        daily_essentials = ['Milk', 'Bread']  # Items bought frequently
        occasional_items = ['Banana', 'Apple', 'Tomato', 'Onion']
        
        for date in dates:
            # Daily essentials (80% chance)
            for item in daily_essentials:
                if np.random.random() < 0.8:
                    orders_data.append({
                        'customer_id': test_user,
                        'sku_code': item,
                        'delivery_date': date,
                        'ordered_qty': 1
                    })
            
            # Occasional items (30% chance)
            for item in occasional_items:
                if np.random.random() < 0.3:
                    orders_data.append({
                        'customer_id': test_user,
                        'sku_code': item,
                        'delivery_date': date,
                        'ordered_qty': 1
                    })
        
        orders_df = pd.DataFrame(orders_data)
        
        print(f"   ✅ Created {len(orders_df)} orders over {len(dates)} days")
        
        # Test 1: Daily Purchase Pattern Analysis
        print(f"\n🧠 Test 1: Daily Purchase Pattern Analysis")
        print("-" * 40)
        
        current_date = pd.Timestamp('2025-07-10')
        rec_system.analyze_daily_purchase_patterns(orders_df, current_date)
        
        if test_user in rec_system.user_daily_essentials:
            essentials = rec_system.user_daily_essentials[test_user]
            print(f"   ✅ Daily essentials detected: {len(essentials)} items")
            for item, freq in essentials.items():
                print(f"      • {item}: {freq:.1%} purchase frequency")
        else:
            print(f"   ⚠️ No daily essentials detected")
        
        # Test 2: Cooldown System
        print(f"\n❄️ Test 2: Recommendation Cooldown System")
        print("-" * 40)
        
        test_date = pd.Timestamp('2025-07-10')
        
        # Simulate recommending an item
        rec_system.update_recommendation_tracking(test_user, 'Milk', test_date)
        
        # Check if it's in cooldown the next day
        next_day = test_date + timedelta(days=1)
        is_cooldown = rec_system.is_item_in_cooldown(test_user, 'Milk', next_day)
        print(f"   📅 Milk recommended on {test_date.strftime('%Y-%m-%d')}")
        print(f"   ❄️ Is Milk in cooldown on {next_day.strftime('%Y-%m-%d')}? {is_cooldown}")
        
        # Check daily essential override
        is_essential = rec_system.is_daily_essential(test_user, 'Milk')
        print(f"   🎯 Is Milk a daily essential? {is_essential}")
        
        if is_essential:
            print(f"   ✅ Daily essentials can override cooldown")
        
        # Test 3: Alternative Recommendations
        print(f"\n🔄 Test 3: Alternative Recommendation Generation")
        print("-" * 40)
        
        # Create a simple user profile
        user_profile = {
            'purchased_items': ['Milk', 'Bread', 'Banana'],
            'category_preferences': {'Dairy': 0.4, 'Bakery': 0.3, 'Fruit': 0.3}
        }
        
        # Create a simple similarity dict
        similarity_dict = {
            'Milk': {'Eggs': 0.6},  # Milk is similar to Eggs (both dairy)
            'Bread': {'Potato': 0.4},  # Bread somewhat similar to Potato
            'Banana': {'Apple': 0.8}  # Banana very similar to Apple
        }
        
        # Test alternatives for blocked items
        blocked_items = ['Milk', 'Banana']
        
        for blocked_item in blocked_items:
            alternatives = rec_system.get_alternative_recommendations(
                test_user, blocked_item, user_profile, similarity_dict, num_alternatives=2
            )
            print(f"   🚫 Blocked item: {blocked_item}")
            print(f"   🔄 Alternatives: {alternatives}")
        
        # Test 4: Full Deduplication Process
        print(f"\n🎯 Test 4: Full Deduplication Process")
        print("-" * 40)
        
        # Create sample recommendations
        sample_recommendations = [
            {'product_name': 'Milk', 'score': 0.9, 'recommendation_type': 'repurchase'},
            {'product_name': 'Bread', 'score': 0.8, 'recommendation_type': 'repurchase'},
            {'product_name': 'Banana', 'score': 0.7, 'recommendation_type': 'discovery'},
            {'product_name': 'Apple', 'score': 0.6, 'recommendation_type': 'discovery'},
            {'product_name': 'Tomato', 'score': 0.5, 'recommendation_type': 'discovery'}
        ]
        
        # Simulate that Milk and Banana were recommended yesterday
        yesterday = current_date - timedelta(days=1)
        rec_system.update_recommendation_tracking(test_user, 'Milk', yesterday)
        rec_system.update_recommendation_tracking(test_user, 'Banana', yesterday)
        
        print(f"   📝 Original recommendations: {[r['product_name'] for r in sample_recommendations]}")
        
        # Apply deduplication
        deduplicated = rec_system.apply_intelligent_deduplication(
            sample_recommendations, test_user, current_date, user_profile, similarity_dict
        )
        
        print(f"   🧠 After deduplication: {[r['product_name'] for r in deduplicated]}")
        
        # Show what happened to each item
        original_items = set(r['product_name'] for r in sample_recommendations)
        final_items = set(r['product_name'] for r in deduplicated)
        
        for item in original_items:
            if item in final_items:
                is_essential = rec_system.is_daily_essential(test_user, item)
                was_cooldown = rec_system.is_item_in_cooldown(test_user, item, current_date)
                if is_essential and was_cooldown:
                    print(f"      ✅ {item}: Kept (daily essential, overrides cooldown)")
                elif not was_cooldown:
                    print(f"      ✅ {item}: Kept (not in cooldown)")
                else:
                    print(f"      ✅ {item}: Kept (other reason)")
            else:
                print(f"      🔄 {item}: Replaced with alternative")
        
        # Show any new items (alternatives)
        new_items = final_items - original_items
        if new_items:
            print(f"      🆕 New alternatives: {list(new_items)}")
        
        print(f"\n🎉 All component tests completed successfully!")
        print(f"💡 The deduplication system is working as expected.")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_deduplication_components()
