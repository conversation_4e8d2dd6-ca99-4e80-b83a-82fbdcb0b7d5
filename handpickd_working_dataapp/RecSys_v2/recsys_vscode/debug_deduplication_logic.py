#!/usr/bin/env python3
"""
Debug script to check why the deduplication logic is not working as expected
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

def debug_deduplication_logic():
    """Debug the deduplication logic for specific users"""
    
    print("🔍 Debugging Deduplication Logic")
    print("=" * 50)
    
    try:
        # Load the current CSV to see the problematic users
        df = pd.read_csv('user_segment_weekly_comparison.csv')
        
        # Find a user with identical recommendations
        problem_user = None
        for customer_id in df['customer_id'].unique()[:5]:
            customer_data = df[df['customer_id'] == customer_id].sort_values('delivery_date')
            if len(customer_data) > 1:
                # Check for identical consecutive recommendations
                prev_recs = None
                for _, row in customer_data.iterrows():
                    current_recs = row['recommended_items']
                    if prev_recs is not None and prev_recs == current_recs:
                        problem_user = customer_id
                        break
                    prev_recs = current_recs
                if problem_user:
                    break
        
        if not problem_user:
            print("❌ No problematic users found")
            return
        
        print(f"🎯 Found problematic user: {problem_user[:8]}...")
        
        # Show their recommendations across dates
        user_data = df[df['customer_id'] == problem_user].sort_values('delivery_date')
        print(f"\n📅 User's recommendations across dates:")
        for _, row in user_data.iterrows():
            print(f"   {row['delivery_date']}: {row['recommended_items']}")
        
        # Now let's manually test the deduplication system
        print(f"\n🧪 Testing deduplication system manually...")
        
        # Import the recommendation system
        from demo1 import UltraOptimizedRecommendationSystem
        
        # Load data
        try:
            catalogue_df = pd.read_csv('catalogue_rc_with_parent_names.csv')
            customer_orders_df = pd.read_csv('customer_orders.csv')
            repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
        except FileNotFoundError as e:
            print(f"❌ Could not load data files: {e}")
            return
        
        # Initialize system
        rec_system = UltraOptimizedRecommendationSystem(
            user_repurchase_ratios_df=repurchase_ratios_df,
            product_name_column='name'
        )
        
        # Precompute features
        rec_system.precompute_static_features(customer_orders_df, catalogue_df)
        
        # Convert dates
        customer_orders_df['delivery_date'] = pd.to_datetime(customer_orders_df['delivery_date'])
        
        # Test with consecutive dates
        test_dates = [
            pd.Timestamp('2025-07-09'),
            pd.Timestamp('2025-07-10')
        ]
        
        print(f"\n🔍 Testing deduplication for consecutive dates:")
        
        recommendations_by_date = {}
        
        for eval_date in test_dates:
            print(f"\n📅 Testing {eval_date.strftime('%Y-%m-%d')}...")
            
            # Filter training data
            train_data = customer_orders_df[customer_orders_df['delivery_date'] < eval_date]
            
            # Build matrices
            model_data = rec_system.build_matrices_ultra_fast(train_data, eval_date)
            user_profiles = rec_system.build_user_profiles_lightning_fast(
                model_data['user_item_matrix'], 
                model_data['user_item_qty_matrix']
            )
            
            if problem_user not in user_profiles:
                print(f"   ⚠️ User not found in profiles")
                continue
            
            # Analyze daily patterns
            rec_system.analyze_daily_purchase_patterns(customer_orders_df, eval_date)
            
            # Check user's daily essentials
            if problem_user in rec_system.user_daily_essentials:
                essentials = rec_system.user_daily_essentials[problem_user]
                print(f"   🎯 Daily essentials: {list(essentials.keys())[:5]}")
            else:
                print(f"   ⚠️ No daily essentials found")
            
            # Check cooldown status for some items
            if eval_date == test_dates[1]:  # Second date
                print(f"   ❄️ Checking cooldown status:")
                sample_items = ['Tomato Hybrid', 'Banana Robusta', 'Lemon', 'Onion Nashik - Medium Size']
                for item in sample_items:
                    in_cooldown = rec_system.is_item_in_cooldown(problem_user, item, eval_date)
                    is_essential = rec_system.is_daily_essential(problem_user, item)
                    print(f"      {item}: cooldown={in_cooldown}, essential={is_essential}")
            
            # Generate recommendations WITHOUT deduplication
            recs_without = rec_system.get_recommendations_with_intelligent_deduplication(
                problem_user, user_profiles[problem_user], model_data['similarity_dict'], 
                eval_date, 10, enable_deduplication=False
            )
            
            # Generate recommendations WITH deduplication
            recs_with = rec_system.get_recommendations_with_intelligent_deduplication(
                problem_user, user_profiles[problem_user], model_data['similarity_dict'], 
                eval_date, 10, enable_deduplication=True
            )
            
            without_items = [r['product_name'] for r in recs_without]
            with_items = [r['product_name'] for r in recs_with]
            
            recommendations_by_date[eval_date] = {
                'without': without_items,
                'with': with_items
            }
            
            print(f"   📊 Without deduplication: {len(without_items)} items")
            print(f"   🧠 With deduplication: {len(with_items)} items")
            print(f"   📝 Sample without: {without_items[:5]}")
            print(f"   🎯 Sample with: {with_items[:5]}")
        
        # Compare results
        if len(recommendations_by_date) == 2:
            date1, date2 = test_dates
            
            without_1 = set(recommendations_by_date[date1]['without'])
            without_2 = set(recommendations_by_date[date2]['without'])
            with_1 = set(recommendations_by_date[date1]['with'])
            with_2 = set(recommendations_by_date[date2]['with'])
            
            without_similarity = len(without_1.intersection(without_2)) / len(without_1.union(without_2)) if len(without_1.union(without_2)) > 0 else 0
            with_similarity = len(with_1.intersection(with_2)) / len(with_1.union(with_2)) if len(with_1.union(with_2)) > 0 else 0
            
            print(f"\n📊 Comparison Results:")
            print(f"   Without deduplication similarity: {without_similarity:.1%}")
            print(f"   With deduplication similarity: {with_similarity:.1%}")
            
            if with_similarity < without_similarity:
                print(f"   ✅ Deduplication is working! Reduced similarity by {(without_similarity - with_similarity):.1%}")
            elif with_similarity == without_similarity:
                print(f"   ⚠️ Deduplication had no effect")
            else:
                print(f"   ❌ Deduplication increased similarity (unexpected)")
            
            # Show what changed
            common_without = without_1.intersection(without_2)
            common_with = with_1.intersection(with_2)
            
            print(f"\n🔍 Detailed Analysis:")
            print(f"   Common items without deduplication: {len(common_without)}")
            print(f"   Common items with deduplication: {len(common_with)}")
            
            if common_without:
                print(f"   Sample common (without): {list(common_without)[:3]}")
            if common_with:
                print(f"   Sample common (with): {list(common_with)[:3]}")
        
        # Check if the issue is in the tracking system
        print(f"\n🔍 Checking recommendation tracking:")
        if problem_user in rec_system.user_last_recommended:
            tracked_items = rec_system.user_last_recommended[problem_user]
            print(f"   📝 Tracked items: {len(tracked_items)}")
            for item, date in list(tracked_items.items())[:5]:
                print(f"      {item}: last recommended on {date}")
        else:
            print(f"   ⚠️ No tracking data found for user")
        
    except Exception as e:
        print(f"❌ Error during debugging: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_deduplication_logic()
