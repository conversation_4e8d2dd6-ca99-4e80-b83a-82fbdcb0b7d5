#!/usr/bin/env python3
"""
Diagnostic script to check what's happening with segment_weekly_df recommendations
"""

import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def diagnose_segment_weekly():
    """Diagnose the segment_weekly_df issue"""
    
    print("🔍 Diagnosing segment_weekly_df Recommendation Issues")
    print("=" * 60)
    
    try:
        # Load the current segment_weekly_df
        print("📊 Loading current segment_weekly_df...")
        
        try:
            df = pd.read_csv('user_segment_weekly_comparison.csv')
            print(f"   ✅ Loaded segment_weekly_df: {df.shape}")
            print(f"   📅 Date range: {df['delivery_date'].min()} to {df['delivery_date'].max()}")
            print(f"   👥 Unique customers: {df['customer_id'].nunique()}")
        except FileNotFoundError:
            print("   ❌ user_segment_weekly_comparison.csv not found")
            return
        
        # Check for identical recommendations across dates
        print(f"\n🔍 Analyzing recommendation patterns...")
        
        identical_patterns = []
        customers_with_issues = []
        
        for customer_id in df['customer_id'].unique()[:10]:  # Check first 10 customers
            customer_data = df[df['customer_id'] == customer_id].sort_values('delivery_date')
            
            if len(customer_data) > 1:
                print(f"\n👤 Customer: {customer_id[:8]}...")
                
                prev_recs = None
                identical_count = 0
                
                for _, row in customer_data.iterrows():
                    current_recs = row['recommended_items']
                    date = row['delivery_date']
                    
                    print(f"   📅 {date}: {current_recs}")
                    
                    if prev_recs is not None and prev_recs == current_recs:
                        identical_count += 1
                        identical_patterns.append({
                            'customer_id': customer_id,
                            'date': date,
                            'recommendations': current_recs
                        })
                    
                    prev_recs = current_recs
                
                if identical_count > 0:
                    print(f"   ⚠️ Found {identical_count} identical consecutive recommendations")
                    customers_with_issues.append(customer_id)
                else:
                    print(f"   ✅ All recommendations are different")
        
        print(f"\n📊 Summary:")
        print(f"   - Customers analyzed: 10")
        print(f"   - Customers with identical patterns: {len(customers_with_issues)}")
        print(f"   - Total identical patterns found: {len(identical_patterns)}")
        
        # Check if the issue is with the data source
        print(f"\n🔍 Checking data source issues...")
        
        # Load the recommendations_df if it exists
        try:
            # Check if there are any recent recommendation files
            import os
            files = [f for f in os.listdir('.') if 'recommendation' in f.lower() and f.endswith('.csv')]
            print(f"   📁 Found recommendation files: {files}")
            
            # Check if the recommendations_df variable exists in the current session
            print(f"   🔍 Checking if recommendations were generated with deduplication...")
            
            # Look for any files that might contain the actual recommendations
            if 'user_segment_weekly_comparison.csv' in files:
                print(f"   📊 Current segment_weekly_df is from a previous run")
                print(f"   💡 Need to regenerate with the new deduplication system")
            
        except Exception as e:
            print(f"   ⚠️ Could not check recommendation files: {e}")
        
        # Check the timestamp of the file
        import os
        if os.path.exists('user_segment_weekly_comparison.csv'):
            mtime = os.path.getmtime('user_segment_weekly_comparison.csv')
            file_time = datetime.fromtimestamp(mtime)
            print(f"   📅 File last modified: {file_time}")
            
            # Check if it's recent (within last hour)
            if datetime.now() - file_time < timedelta(hours=1):
                print(f"   ✅ File is recent - likely from current session")
            else:
                print(f"   ⚠️ File is old - likely from previous session without deduplication")
        
        # Provide recommendations
        print(f"\n💡 Recommendations:")
        if len(customers_with_issues) > 0:
            print(f"   1. The segment_weekly_df shows identical recommendations across dates")
            print(f"   2. This suggests the deduplication system may not be fully active")
            print(f"   3. Possible causes:")
            print(f"      - File is from a previous run without deduplication")
            print(f"      - Deduplication system is disabled")
            print(f"      - Date filtering issue in segment_weekly_df creation")
            print(f"   4. Solutions:")
            print(f"      - Re-run the evaluation with deduplication enabled")
            print(f"      - Check if deduplication_enabled = True")
            print(f"      - Verify the recommendations_df contains varied recommendations")
        else:
            print(f"   ✅ No issues found - deduplication appears to be working")
        
        # Check if we can access the current recommendations_df
        print(f"\n🔍 Checking current session data...")
        try:
            # Try to import and check if variables exist
            import sys
            if 'demo1' in sys.modules:
                print(f"   ✅ demo1 module is loaded")
                # Check if we can access any global variables
                demo1 = sys.modules['demo1']
                if hasattr(demo1, 'recommendations_df'):
                    recs_df = demo1.recommendations_df
                    print(f"   📊 Found recommendations_df: {recs_df.shape}")
                    
                    # Check for diversity in recommendations
                    if 'product_name' in recs_df.columns and 'customer_id' in recs_df.columns:
                        sample_customer = recs_df['customer_id'].iloc[0]
                        customer_recs = recs_df[recs_df['customer_id'] == sample_customer]
                        
                        if 'recommendation_date' in customer_recs.columns:
                            dates = customer_recs['recommendation_date'].unique()
                            print(f"   📅 Sample customer has recommendations for {len(dates)} dates")
                            
                            for date in dates[:3]:
                                date_recs = customer_recs[customer_recs['recommendation_date'] == date]
                                items = list(date_recs['product_name'].head(5))
                                print(f"      {date}: {items}")
                else:
                    print(f"   ⚠️ recommendations_df not found in demo1 module")
            else:
                print(f"   ⚠️ demo1 module not loaded")
        except Exception as e:
            print(f"   ⚠️ Could not check session data: {e}")
        
    except Exception as e:
        print(f"❌ Error during diagnosis: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_segment_weekly()
