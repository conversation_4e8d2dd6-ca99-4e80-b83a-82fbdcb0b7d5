{"cells": [{"cell_type": "code", "execution_count": null, "id": "da913d02", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 ULTRA-OPTIMIZED PRECISION & RECALL EVALUATION WITH PERSONALIZED k VALUES\n", "======================================================================\n", "🎯 Maximum speed optimization with intelligent caching\n", "🔧 NEW: Configurable product name column (name vs sku_parent_name)\n", "🎯 NEW: Personalized k values based on user's last 15 purchase patterns\n", "======================================================================\n", "\n", "🔧 SETUP PHASE:\n", "📊 Available columns in catalogue: ['store_id', 'sku_code', 'name', 'category_name', 'unit_of_measurement', 'add_to_cart_uom', 'per_pcs_weight', 'packet_description', 'max_quantity', 'price_brackets', 'is_pesticide_free_available', 'pesticide_free_charges', 'grades_', 'synonym', 'sku_parent_name']\n", "🔧 Using 'name' column for product identification\n", "📊 Unique 'name' values: 368\n", "📊 Unique 'sku_parent_name' values: 164\n", "🔧 Using 'name' column for product identification\n", "✅ Loaded dynamic repurchase ratios for 1,110 users\n", "   Average repurchase ratio: 0.772\n", "🔧 Pre-computing static features using 'name' column (one-time setup)...\n", "📊 Creating name-to-SKU mappings...\n", "📊 Converting orders to use name...\n", "✅ Pre-computed 368 product categories using 'name'\n", "✅ Pre-computed 299 popular products\n", "✅ Pre-computed mappings: 368 name values → 1004 SKUs\n", "✅ Pre-computed global statistics using name\n", "🚀 HIGH-RECALL PRECISION & RECALL EVALUATION WITH PERSONALIZED k VALUES\n", "🎯 Using median items from last 15 purchases as personalized k for each user\n", "🎯 TARGET: 80%+ RECALL with Multi-Strategy Ensemble Approach using 'name' column\n", "======================================================================\n", "🎯 Calculating personalized k values based on median items in last 15 purchases...\n", "✅ Calculated personalized k for 1,110 users\n", "   Median k across all users: 6.0\n", "   Mean k across all users: 7.7\n", "   Min k: 3, <PERSON> k: 37\n", "   Most common k values: {3: 124, 4: 141, 5: 148, 6: 157, 7: 131}\n", "📊 Using personalized k values (median: 6)\n", "📊 Pre-computing test data for all dates using name...\n", "\n", "📅 2025-07-04\n", "   📊 Train: 550972 orders | Test: 456 users\n", "⚡ Building matrices (vectorized using name)...\n", "⚡ Computing AGGRESSIVE similarity matrix for high recall using name...\n", "✅ Built matrices using name: (1110, 301), similarity: 200 items\n", "⚡ Building user profiles (lightning fast using name)...\n", "✅ Built 1110 user profiles using name\n", "🧠 Analyzing daily purchase patterns for 2025-07-04...\n", "   📊 Analyzing 36174 orders from 2025-06-20 to 2025-07-04\n", "   ✅ Analyzed 1048 users\n", "   🎯 Found 154 daily essential items across all users\n", "   📈 Average daily essentials per user: 0.1\n", "   👤 Sample user 1ed8e3de...: 1 daily essentials\n", "      • Lemon: 50.0% purchase frequency\n", "   🧠 Generating INTELLIGENT DEDUPLICATED recommendations using Multi-Strategy Ensemble with name...\n", "🔍 DEBUG: Deduplication for user 4bcb4707... on 2025-07-04\n", "   📝 Input recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion Nashik - Medium Size', 'Ginger New', '<PERSON><PERSON>']\n", "      ✅ ALLOWED Tomato Hybrid (reason: essential)\n", "      ✅ ALLOWED Cucumber English (reason: no_cooldown)\n", "      ✅ ALLOWED Onion Nashik - Medium Size (reason: no_cooldown)\n", "      ✅ ALLOWED Ginger New (reason: no_cooldown)\n", "      ✅ ALLOWED Banana Robusta (reason: no_cooldown)\n", "      ✅ ALLOWED Coriander (<PERSON><PERSON><PERSON>) (reason: no_cooldown)\n", "      ✅ ALLOWED Fresh Malai Paneer (reason: no_cooldown)\n", "      ✅ ALLOWED Lemon (reason: no_cooldown)\n", "      ✅ ALLOWED Cabbage (reason: no_cooldown)\n", "      ✅ ALLOWED Garlic Chinese (reason: no_cooldown)\n", "      ✅ ALLOWED Mushroom (reason: no_cooldown)\n", "      ✅ ALLOWED <PERSON><PERSON><PERSON> (reason: no_cooldown)\n", "   🎯 Final recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion <PERSON>ik - Medium Size', 'Ginger New', '<PERSON><PERSON>']\n", "   📊 Blocked 0 items, generated 12 final recommendations\n", "🔍 DEBUG: Deduplication for user 570fa519... on 2025-07-04\n", "   📝 Input recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', '<PERSON><PERSON> (Bottle Gourd) - Medium', '<PERSON><PERSON> (Sponge Gourd)']\n", "      ✅ ALLOWED Banana Robusta (reason: no_cooldown)\n", "      ✅ ALLOWED Tomato Hybrid (reason: no_cooldown)\n", "      ✅ ALLOWED <PERSON><PERSON> (Bottle Gourd) - Medium (reason: no_cooldown)\n", "      ✅ ALLOWED Torai (Sponge Gourd) (reason: no_cooldown)\n", "   🎯 Final recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', '<PERSON><PERSON> (Bottle Gourd) - Medium', '<PERSON><PERSON> (Sponge Gourd)']\n", "   📊 Blocked 0 items, generated 4 final recommendations\n", "   📊 Computing metrics using name...\n", "   👥 Users: 456 | P@k: 0.260 | R@k: 0.320 | Med k: 6\n", "   ⏱️ Day completed in 7.0s\n", "\n", "📅 2025-07-05\n", "   📊 Train: 554090 orders | Test: 402 users\n", "⚡ Building matrices (vectorized using name)...\n", "⚡ Computing AGGRESSIVE similarity matrix for high recall using name...\n", "✅ Built matrices using name: (1110, 301), similarity: 200 items\n", "⚡ Building user profiles (lightning fast using name)...\n", "✅ Built 1110 user profiles using name\n", "🧠 Analyzing daily purchase patterns for 2025-07-05...\n", "   📊 Analyzing 36123 orders from 2025-06-21 to 2025-07-05\n", "   ✅ Analyzed 1048 users\n", "   🎯 Found 149 daily essential items across all users\n", "   📈 Average daily essentials per user: 0.1\n", "   👤 Sample user 1ed8e3de...: 1 daily essentials\n", "      • Lemon: 50.0% purchase frequency\n", "🔍 DEBUG: User 570fa519... tracking: 4 items\n", "      Banana Robusta: 1 days ago\n", "      Tomato Hybrid: 1 days ago\n", "      <PERSON><PERSON> (Bottle Gourd) - Medium: 1 days ago\n", "🔍 DEBUG: User 4bcb4707... tracking: 12 items\n", "      Tomato Hybrid: 1 days ago\n", "      Cucumber English: 1 days ago\n", "      Onion Nashik - Medium Size: 1 days ago\n", "   🧠 Generating INTELLIGENT DEDUPLICATED recommendations using Multi-Strategy Ensemble with name...\n", "🔍 DEBUG: Deduplication for user 4bcb4707... on 2025-07-05\n", "   📝 Input recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion Nashik - Medium Size', 'Ginger New', '<PERSON><PERSON>']\n", "      🔍 Tomato Hybrid: last_rec=07-04, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: essential)\n", "      🔍 Cucumber English: last_rec=07-04, days_since=1, cooldown=True\n", "      ✅ ALLOWED Cucumber English (reason: min_recs_needed)\n", "      🔍 Onion Nashik - Medium Size: last_rec=07-04, days_since=1, cooldown=True\n", "      ✅ ALLOWED Onion Nashik - Medium Size (reason: min_recs_needed)\n", "      🔍 Ginger New: last_rec=07-04, days_since=1, cooldown=True\n", "      ✅ ALLOWED Ginger New (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON>: last_rec=07-04, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Coriander (<PERSON><PERSON><PERSON>): last_rec=07-04, days_since=1, cooldown=True\n", "      ✅ ALLOWED Coriander (<PERSON>hania) (reason: min_recs_needed)\n", "      🔍 Fresh Malai Paneer: last_rec=07-04, days_since=1, cooldown=True\n", "      ❌ BLOCKED Fresh Malai Paneer (in cooldown, not essential, have enough recs)\n", "      🔍 Lemon: last_rec=07-04, days_since=1, cooldown=True\n", "      ❌ BLOCKED Lemon (in cooldown, not essential, have enough recs)\n", "      🔍 Cabbage: last_rec=07-04, days_since=1, cooldown=True\n", "      ❌ BLOCKED Cabbage (in cooldown, not essential, have enough recs)\n", "      🔍 Garlic Chinese: last_rec=07-04, days_since=1, cooldown=True\n", "      ❌ BLOCKED Garlic Chinese (in cooldown, not essential, have enough recs)\n", "      🔍 Mushroom: last_rec=07-04, days_since=1, cooldown=True\n", "      ❌ BLOCKED Mushroom (in cooldown, not essential, have enough recs)\n", "      🔍 <PERSON><PERSON><PERSON>: last_rec=07-04, days_since=1, cooldown=True\n", "      ❌ BLOCKED Karela Desi (in cooldown, not essential, have enough recs)\n", "   🎯 Final recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion <PERSON>ik - Medium Size', 'Ginger New', '<PERSON><PERSON>']\n", "   📊 Blocked 6 items, generated 12 final recommendations\n", "🔍 DEBUG: Deduplication for user 570fa519... on 2025-07-05\n", "   📝 Input recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', '<PERSON><PERSON> (Bottle Gourd) - Medium', '<PERSON><PERSON> (Sponge Gourd)']\n", "      🔍 <PERSON><PERSON>: last_rec=07-04, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Tomato Hybrid: last_rec=07-04, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON> (<PERSON><PERSON> Gourd) - Medium: last_rec=07-04, days_since=1, cooldown=True\n", "      ❌ BLOCKED <PERSON> (Bottle Gourd) - Medium (in cooldown, not essential, have enough recs)\n", "      🔍 Torai (Sponge Gourd): last_rec=07-04, days_since=1, cooldown=True\n", "      ❌ BLOCKED Torai (Sponge Gourd) (in cooldown, not essential, have enough recs)\n", "   🎯 Final recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', 'Fresh Malai Paneer', 'Fresh Malai Paneer']\n", "   📊 Blocked 2 items, generated 4 final recommendations\n", "   📊 Computing metrics using name...\n", "   👥 Users: 402 | P@k: 0.241 | R@k: 0.276 | Med k: 6\n", "   ⏱️ Day completed in 10.4s\n", "\n", "📅 2025-07-06\n", "   📊 Train: 556889 orders | Test: 399 users\n", "⚡ Building matrices (vectorized using name)...\n", "⚡ Computing AGGRESSIVE similarity matrix for high recall using name...\n", "✅ Built matrices using name: (1110, 301), similarity: 200 items\n", "⚡ Building user profiles (lightning fast using name)...\n", "✅ Built 1110 user profiles using name\n", "🧠 Analyzing daily purchase patterns for 2025-07-06...\n", "   📊 Analyzing 36417 orders from 2025-06-22 to 2025-07-06\n", "   ✅ Analyzed 1049 users\n", "   🎯 Found 156 daily essential items across all users\n", "   📈 Average daily essentials per user: 0.1\n", "   👤 Sample user 1ed8e3de...: 1 daily essentials\n", "      • Lemon: 50.0% purchase frequency\n", "🔍 DEBUG: User 570fa519... tracking: 5 items\n", "      Banana Robusta: 1 days ago\n", "      Tomato Hybrid: 1 days ago\n", "      <PERSON><PERSON> (Bottle Gourd) - Medium: 2 days ago\n", "🔍 DEBUG: User 4bcb4707... tracking: 13 items\n", "      Tomato Hybrid: 1 days ago\n", "      Cucumber English: 1 days ago\n", "      Onion Nashik - Medium Size: 1 days ago\n", "   🧠 Generating INTELLIGENT DEDUPLICATED recommendations using Multi-Strategy Ensemble with name...\n", "🔍 DEBUG: Deduplication for user 4bcb4707... on 2025-07-06\n", "   📝 Input recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion Nashik - Medium Size', 'Ginger New', '<PERSON><PERSON>']\n", "      🔍 Tomato Hybrid: last_rec=07-05, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: essential)\n", "      🔍 Cucumber English: last_rec=07-05, days_since=1, cooldown=True\n", "      ✅ ALLOWED Cucumber English (reason: min_recs_needed)\n", "      🔍 Onion Nashik - Medium Size: last_rec=07-05, days_since=1, cooldown=True\n", "      ✅ ALLOWED Onion Nashik - Medium Size (reason: min_recs_needed)\n", "      🔍 Ginger New: last_rec=07-05, days_since=1, cooldown=True\n", "      ✅ ALLOWED Ginger New (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON>: last_rec=07-05, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Coriander (<PERSON><PERSON><PERSON>): last_rec=07-05, days_since=1, cooldown=True\n", "      ✅ ALLOWED Coriander (<PERSON>hania) (reason: min_recs_needed)\n", "      🔍 Fresh Malai Paneer: last_rec=07-05, days_since=1, cooldown=True\n", "      ❌ BLOCKED Fresh Malai Paneer (in cooldown, not essential, have enough recs)\n", "      🔍 Lemon: last_rec=07-04, days_since=2, cooldown=False\n", "      ✅ ALLOWED Lemon (reason: no_cooldown)\n", "      🔍 Cabbage: last_rec=07-04, days_since=2, cooldown=False\n", "      ✅ ALLOWED Cabbage (reason: no_cooldown)\n", "      🔍 Garlic Chinese: last_rec=07-04, days_since=2, cooldown=False\n", "      ✅ ALLOWED Garlic Chinese (reason: no_cooldown)\n", "      🔍 Mushroom: last_rec=07-04, days_since=2, cooldown=False\n", "      ✅ ALLOWED Mushroom (reason: no_cooldown)\n", "      🔍 <PERSON><PERSON><PERSON>: last_rec=07-04, days_since=2, cooldown=False\n", "      ✅ ALLOWED <PERSON><PERSON><PERSON> (reason: no_cooldown)\n", "   🎯 Final recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion <PERSON>ik - Medium Size', 'Ginger New', '<PERSON><PERSON>']\n", "   📊 Blocked 1 items, generated 12 final recommendations\n", "🔍 DEBUG: Deduplication for user 570fa519... on 2025-07-06\n", "   📝 Input recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', '<PERSON><PERSON> (Bottle Gourd) - Medium', '<PERSON><PERSON> (Sponge Gourd)']\n", "      🔍 <PERSON><PERSON>: last_rec=07-05, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Tomato Hybrid: last_rec=07-05, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON> (<PERSON><PERSON> Gourd) - Medium: last_rec=07-04, days_since=2, cooldown=False\n", "      ✅ ALLOWED <PERSON><PERSON> (Bottle Gourd) - Medium (reason: no_cooldown)\n", "      🔍 Torai (Sponge Gourd): last_rec=07-04, days_since=2, cooldown=False\n", "      ✅ ALLOWED Torai (Sponge Gourd) (reason: no_cooldown)\n", "   🎯 Final recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', '<PERSON><PERSON> (Bottle Gourd) - Medium', '<PERSON><PERSON> (Sponge Gourd)']\n", "   📊 Blocked 0 items, generated 4 final recommendations\n", "   📊 Computing metrics using name...\n", "   👥 Users: 399 | P@k: 0.292 | R@k: 0.336 | Med k: 6\n", "   ⏱️ Day completed in 7.2s\n", "\n", "📅 2025-07-07\n", "   📊 Train: 560059 orders | Test: 480 users\n", "⚡ Building matrices (vectorized using name)...\n", "⚡ Computing AGGRESSIVE similarity matrix for high recall using name...\n", "✅ Built matrices using name: (1110, 301), similarity: 200 items\n", "⚡ Building user profiles (lightning fast using name)...\n", "✅ Built 1110 user profiles using name\n", "🧠 Analyzing daily purchase patterns for 2025-07-07...\n", "   📊 Analyzing 36700 orders from 2025-06-23 to 2025-07-07\n", "   ✅ Analyzed 1052 users\n", "   🎯 Found 152 daily essential items across all users\n", "   📈 Average daily essentials per user: 0.1\n", "🔍 DEBUG: User 570fa519... tracking: 5 items\n", "      Banana Robusta: 1 days ago\n", "      Tomato Hybrid: 1 days ago\n", "      <PERSON><PERSON> (Bottle Gourd) - Medium: 1 days ago\n", "🔍 DEBUG: User 4bcb4707... tracking: 13 items\n", "      Tomato Hybrid: 1 days ago\n", "      Cucumber English: 1 days ago\n", "      Onion Nashik - Medium Size: 1 days ago\n", "   🧠 Generating INTELLIGENT DEDUPLICATED recommendations using Multi-Strategy Ensemble with name...\n", "🔍 DEBUG: Deduplication for user 4bcb4707... on 2025-07-07\n", "   📝 Input recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion Nashik - Medium Size', 'Ginger New', '<PERSON><PERSON>']\n", "      🔍 Tomato Hybrid: last_rec=07-06, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: essential)\n", "      🔍 Cucumber English: last_rec=07-06, days_since=1, cooldown=True\n", "      ✅ ALLOWED Cucumber English (reason: min_recs_needed)\n", "      🔍 Onion Nashik - Medium Size: last_rec=07-06, days_since=1, cooldown=True\n", "      ✅ ALLOWED Onion Nashik - Medium Size (reason: min_recs_needed)\n", "      🔍 Ginger New: last_rec=07-06, days_since=1, cooldown=True\n", "      ✅ ALLOWED Ginger New (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON>: last_rec=07-06, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Coriander (<PERSON><PERSON><PERSON>): last_rec=07-06, days_since=1, cooldown=True\n", "      ✅ ALLOWED Coriander (<PERSON>hania) (reason: min_recs_needed)\n", "      🔍 Fresh Malai Paneer: last_rec=07-05, days_since=2, cooldown=False\n", "      ✅ ALLOWED Fresh Malai Paneer (reason: no_cooldown)\n", "      🔍 Lemon: last_rec=07-06, days_since=1, cooldown=True\n", "      ❌ BLOCKED Lemon (in cooldown, not essential, have enough recs)\n", "      🔍 Cabbage: last_rec=07-06, days_since=1, cooldown=True\n", "      ❌ BLOCKED Cabbage (in cooldown, not essential, have enough recs)\n", "      🔍 Garlic Chinese: last_rec=07-06, days_since=1, cooldown=True\n", "      ❌ BLOCKED Garlic Chinese (in cooldown, not essential, have enough recs)\n", "      🔍 Mushroom: last_rec=07-06, days_since=1, cooldown=True\n", "      ❌ BLOCKED Mushroom (in cooldown, not essential, have enough recs)\n", "      🔍 <PERSON><PERSON><PERSON>: last_rec=07-06, days_since=1, cooldown=True\n", "      ❌ BLOCKED Karela Desi (in cooldown, not essential, have enough recs)\n", "   🎯 Final recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion <PERSON>ik - Medium Size', 'Ginger New', '<PERSON><PERSON>']\n", "   📊 Blocked 5 items, generated 12 final recommendations\n", "🔍 DEBUG: Deduplication for user 570fa519... on 2025-07-07\n", "   📝 Input recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', '<PERSON><PERSON> (Bottle Gourd) - Medium', '<PERSON><PERSON> (Sponge Gourd)']\n", "      🔍 <PERSON><PERSON>: last_rec=07-06, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Tomato Hybrid: last_rec=07-06, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON> (<PERSON><PERSON> Gourd) - Medium: last_rec=07-06, days_since=1, cooldown=True\n", "      ❌ BLOCKED <PERSON> (Bottle Gourd) - Medium (in cooldown, not essential, have enough recs)\n", "      🔍 Torai (Sponge Gourd): last_rec=07-06, days_since=1, cooldown=True\n", "      ❌ BLOCKED Torai (Sponge Gourd) (in cooldown, not essential, have enough recs)\n", "   🎯 Final recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', 'Fresh Malai Paneer', 'Fresh Malai Paneer']\n", "   📊 Blocked 2 items, generated 4 final recommendations\n", "   📊 Computing metrics using name...\n", "   👥 Users: 480 | P@k: 0.241 | R@k: 0.300 | Med k: 6\n", "   ⏱️ Day completed in 9.9s\n", "\n", "📅 2025-07-08\n", "   📊 Train: 563676 orders | Test: 449 users\n", "⚡ Building matrices (vectorized using name)...\n", "⚡ Computing AGGRESSIVE similarity matrix for high recall using name...\n", "✅ Built matrices using name: (1110, 301), similarity: 200 items\n", "⚡ Building user profiles (lightning fast using name)...\n", "✅ Built 1110 user profiles using name\n", "🧠 Analyzing daily purchase patterns for 2025-07-08...\n", "   📊 Analyzing 36742 orders from 2025-06-24 to 2025-07-08\n", "   ✅ Analyzed 1057 users\n", "   🎯 Found 154 daily essential items across all users\n", "   📈 Average daily essentials per user: 0.1\n", "🔍 DEBUG: User 570fa519... tracking: 5 items\n", "      Banana Robusta: 1 days ago\n", "      Tomato Hybrid: 1 days ago\n", "      <PERSON><PERSON> (Bottle Gourd) - Medium: 2 days ago\n", "🔍 DEBUG: User 4bcb4707... tracking: 13 items\n", "      Tomato Hybrid: 1 days ago\n", "      Cucumber English: 1 days ago\n", "      Onion Nashik - Medium Size: 1 days ago\n", "   🧠 Generating INTELLIGENT DEDUPLICATED recommendations using Multi-Strategy Ensemble with name...\n", "🔍 DEBUG: Deduplication for user 4bcb4707... on 2025-07-08\n", "   📝 Input recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion Nashik - Medium Size', 'Ginger New', '<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)']\n", "      🔍 Tomato Hybrid: last_rec=07-07, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: essential)\n", "      🔍 Cucumber English: last_rec=07-07, days_since=1, cooldown=True\n", "      ✅ ALLOWED Cucumber English (reason: min_recs_needed)\n", "      🔍 Onion Nashik - Medium Size: last_rec=07-07, days_since=1, cooldown=True\n", "      ✅ ALLOWED Onion Nashik - Medium Size (reason: min_recs_needed)\n", "      🔍 Ginger New: last_rec=07-07, days_since=1, cooldown=True\n", "      ✅ ALLOWED Ginger New (reason: min_recs_needed)\n", "      🔍 Coriander (<PERSON><PERSON><PERSON>): last_rec=07-07, days_since=1, cooldown=True\n", "      ✅ ALLOWED Coriander (<PERSON>hania) (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON>: last_rec=07-07, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Fresh Malai Paneer: last_rec=07-07, days_since=1, cooldown=True\n", "      ❌ BLOCKED Fresh Malai Paneer (in cooldown, not essential, have enough recs)\n", "      🔍 Garlic Chinese: last_rec=07-06, days_since=2, cooldown=False\n", "      ✅ ALLOWED Garlic Chinese (reason: no_cooldown)\n", "      🔍 Cabbage: last_rec=07-06, days_since=2, cooldown=False\n", "      ✅ ALLOWED Cabbage (reason: no_cooldown)\n", "      🔍 Lemon: last_rec=07-06, days_since=2, cooldown=False\n", "      ✅ ALLOWED Lemon (reason: no_cooldown)\n", "      🔍 Mushroom: last_rec=07-06, days_since=2, cooldown=False\n", "      ✅ ALLOWED Mushroom (reason: no_cooldown)\n", "      🔍 <PERSON><PERSON><PERSON>: last_rec=07-06, days_since=2, cooldown=False\n", "      ✅ ALLOWED <PERSON><PERSON><PERSON> (reason: no_cooldown)\n", "   🎯 Final recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion Nashik - Medium Size', '<PERSON> New', '<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)']\n", "   📊 Blocked 1 items, generated 12 final recommendations\n", "🔍 DEBUG: Deduplication for user 570fa519... on 2025-07-08\n", "   📝 Input recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', '<PERSON><PERSON> (Bottle Gourd) - Medium', '<PERSON><PERSON> (Sponge Gourd)']\n", "      🔍 <PERSON><PERSON>: last_rec=07-07, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Tomato Hybrid: last_rec=07-07, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON> (<PERSON><PERSON> Gourd) - Medium: last_rec=07-06, days_since=2, cooldown=False\n", "      ✅ ALLOWED <PERSON><PERSON> (Bottle Gourd) - Medium (reason: no_cooldown)\n", "      🔍 Torai (Sponge Gourd): last_rec=07-06, days_since=2, cooldown=False\n", "      ✅ ALLOWED Torai (Sponge Gourd) (reason: no_cooldown)\n", "   🎯 Final recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', '<PERSON><PERSON> (Bottle Gourd) - Medium', '<PERSON><PERSON> (Sponge Gourd)']\n", "   📊 Blocked 0 items, generated 4 final recommendations\n", "   📊 Computing metrics using name...\n", "   👥 Users: 449 | P@k: 0.267 | R@k: 0.313 | Med k: 6\n", "   ⏱️ Day completed in 7.2s\n", "\n", "📅 2025-07-09\n", "   📊 Train: 566824 orders | Test: 460 users\n", "⚡ Building matrices (vectorized using name)...\n", "⚡ Computing AGGRESSIVE similarity matrix for high recall using name...\n", "✅ Built matrices using name: (1110, 301), similarity: 200 items\n", "⚡ Building user profiles (lightning fast using name)...\n", "✅ Built 1110 user profiles using name\n", "🧠 Analyzing daily purchase patterns for 2025-07-09...\n", "   📊 Analyzing 36750 orders from 2025-06-25 to 2025-07-09\n", "   ✅ Analyzed 1054 users\n", "   🎯 Found 162 daily essential items across all users\n", "   📈 Average daily essentials per user: 0.2\n", "   👤 Sample user 1ed8e3de...: 1 daily essentials\n", "      • Banana Robusta - Small: 50.0% purchase frequency\n", "🔍 DEBUG: User 570fa519... tracking: 5 items\n", "      Banana Robusta: 1 days ago\n", "      Tomato Hybrid: 1 days ago\n", "      <PERSON><PERSON> (Bottle Gourd) - Medium: 1 days ago\n", "🔍 DEBUG: User 4bcb4707... tracking: 13 items\n", "      Tomato Hybrid: 1 days ago\n", "      Cucumber English: 1 days ago\n", "      Onion Nashik - Medium Size: 1 days ago\n", "   🧠 Generating INTELLIGENT DEDUPLICATED recommendations using Multi-Strategy Ensemble with name...\n", "🔍 DEBUG: Deduplication for user 4bcb4707... on 2025-07-09\n", "   📝 Input recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion Nashik - Medium Size', 'Ginger New', '<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)']\n", "      🔍 Tomato Hybrid: last_rec=07-08, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: essential)\n", "      🔍 Cucumber English: last_rec=07-08, days_since=1, cooldown=True\n", "      ✅ ALLOWED Cucumber English (reason: min_recs_needed)\n", "      🔍 Onion Nashik - Medium Size: last_rec=07-08, days_since=1, cooldown=True\n", "      ✅ ALLOWED Onion Nashik - Medium Size (reason: min_recs_needed)\n", "      🔍 Ginger New: last_rec=07-08, days_since=1, cooldown=True\n", "      ✅ ALLOWED Ginger New (reason: min_recs_needed)\n", "      🔍 Coriander (<PERSON><PERSON><PERSON>): last_rec=07-08, days_since=1, cooldown=True\n", "      ✅ ALLOWED Coriander (<PERSON>hania) (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON>: last_rec=07-08, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Fresh Malai Paneer: last_rec=07-07, days_since=2, cooldown=False\n", "      ✅ ALLOWED Fresh Malai Paneer (reason: no_cooldown)\n", "      🔍 Garlic Chinese: last_rec=07-08, days_since=1, cooldown=True\n", "      ❌ BLOCKED Garlic Chinese (in cooldown, not essential, have enough recs)\n", "      🔍 Cabbage: last_rec=07-08, days_since=1, cooldown=True\n", "      ❌ BLOCKED Cabbage (in cooldown, not essential, have enough recs)\n", "      🔍 Lemon: last_rec=07-08, days_since=1, cooldown=True\n", "      ❌ BLOCKED Lemon (in cooldown, not essential, have enough recs)\n", "      🔍 Mushroom: last_rec=07-08, days_since=1, cooldown=True\n", "      ❌ BLOCKED Mushroom (in cooldown, not essential, have enough recs)\n", "      🔍 <PERSON><PERSON><PERSON>: last_rec=07-08, days_since=1, cooldown=True\n", "      ❌ BLOCKED Karela Desi (in cooldown, not essential, have enough recs)\n", "   🎯 Final recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion Nashik - Medium Size', '<PERSON> New', '<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)']\n", "   📊 Blocked 5 items, generated 12 final recommendations\n", "🔍 DEBUG: Deduplication for user 570fa519... on 2025-07-09\n", "   📝 Input recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', '<PERSON><PERSON> (Bottle Gourd) - Medium', '<PERSON><PERSON> (Sponge Gourd)']\n", "      🔍 <PERSON><PERSON>: last_rec=07-08, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Tomato Hybrid: last_rec=07-08, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON> (<PERSON><PERSON> Gourd) - Medium: last_rec=07-08, days_since=1, cooldown=True\n", "      ❌ BLOCKED <PERSON> (Bottle Gourd) - Medium (in cooldown, not essential, have enough recs)\n", "      🔍 Torai (Sponge Gourd): last_rec=07-08, days_since=1, cooldown=True\n", "      ❌ BLOCKED Torai (Sponge Gourd) (in cooldown, not essential, have enough recs)\n", "   🎯 Final recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', 'Fresh Malai Paneer', 'Fresh Malai Paneer']\n", "   📊 Blocked 2 items, generated 4 final recommendations\n", "   📊 Computing metrics using name...\n", "   👥 Users: 460 | P@k: 0.237 | R@k: 0.284 | Med k: 6\n", "   ⏱️ Day completed in 9.6s\n", "\n", "📅 2025-07-10\n", "   📊 Train: 569925 orders | Test: 453 users\n", "⚡ Building matrices (vectorized using name)...\n", "⚡ Computing AGGRESSIVE similarity matrix for high recall using name...\n", "✅ Built matrices using name: (1110, 301), similarity: 200 items\n", "⚡ Building user profiles (lightning fast using name)...\n", "✅ Built 1110 user profiles using name\n", "🧠 Analyzing daily purchase patterns for 2025-07-10...\n", "   📊 Analyzing 37097 orders from 2025-06-26 to 2025-07-10\n", "   ✅ Analyzed 1059 users\n", "   🎯 Found 156 daily essential items across all users\n", "   📈 Average daily essentials per user: 0.1\n", "   👤 Sample user 1ed8e3de...: 1 daily essentials\n", "      • Banana Robusta - Small: 50.0% purchase frequency\n", "🔍 DEBUG: User 570fa519... tracking: 5 items\n", "      Banana Robusta: 1 days ago\n", "      Tomato Hybrid: 1 days ago\n", "      <PERSON><PERSON> (Bottle Gourd) - Medium: 2 days ago\n", "🔍 DEBUG: User 4bcb4707... tracking: 13 items\n", "      Tomato Hybrid: 1 days ago\n", "      Cucumber English: 1 days ago\n", "      Onion Nashik - Medium Size: 1 days ago\n", "   🧠 Generating INTELLIGENT DEDUPLICATED recommendations using Multi-Strategy Ensemble with name...\n", "🔍 DEBUG: Deduplication for user 4bcb4707... on 2025-07-10\n", "   📝 Input recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion Nashik - Medium Size', 'Ginger New', '<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)']\n", "      🔍 Tomato Hybrid: last_rec=07-09, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: essential)\n", "      🔍 Cucumber English: last_rec=07-09, days_since=1, cooldown=True\n", "      ✅ ALLOWED Cucumber English (reason: min_recs_needed)\n", "      🔍 Onion Nashik - Medium Size: last_rec=07-09, days_since=1, cooldown=True\n", "      ✅ ALLOWED Onion Nashik - Medium Size (reason: min_recs_needed)\n", "      🔍 Ginger New: last_rec=07-09, days_since=1, cooldown=True\n", "      ✅ ALLOWED Ginger New (reason: min_recs_needed)\n", "      🔍 Coriander (<PERSON><PERSON><PERSON>): last_rec=07-09, days_since=1, cooldown=True\n", "      ✅ ALLOWED Coriander (<PERSON>hania) (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON>: last_rec=07-09, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Fresh Malai Paneer: last_rec=07-09, days_since=1, cooldown=True\n", "      ❌ BLOCKED Fresh Malai Paneer (in cooldown, not essential, have enough recs)\n", "      🔍 Garlic Chinese: last_rec=07-08, days_since=2, cooldown=False\n", "      ✅ ALLOWED Garlic Chinese (reason: no_cooldown)\n", "      🔍 Cabbage: last_rec=07-08, days_since=2, cooldown=False\n", "      ✅ ALLOWED Cabbage (reason: no_cooldown)\n", "      🔍 Lemon: last_rec=07-08, days_since=2, cooldown=False\n", "      ✅ ALLOWED Lemon (reason: no_cooldown)\n", "      🔍 Mushroom: last_rec=07-08, days_since=2, cooldown=False\n", "      ✅ ALLOWED Mushroom (reason: no_cooldown)\n", "      ✅ ALLOWED Banana Robusta - Small (reason: no_cooldown)\n", "   🎯 Final recommendations: ['Tomato Hybrid', 'Cucumber English', 'Onion Nashik - Medium Size', '<PERSON> New', '<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)']\n", "   📊 Blocked 1 items, generated 12 final recommendations\n", "🔍 DEBUG: Deduplication for user 570fa519... on 2025-07-10\n", "   📝 Input recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', '<PERSON><PERSON> (Bottle Gourd) - Medium', '<PERSON><PERSON> (Sponge Gourd)']\n", "      🔍 <PERSON><PERSON>: last_rec=07-09, days_since=1, cooldown=True\n", "      ✅ ALLOWED Banana Robusta (reason: min_recs_needed)\n", "      🔍 Tomato Hybrid: last_rec=07-09, days_since=1, cooldown=True\n", "      ✅ ALLOWED Tomato Hybrid (reason: min_recs_needed)\n", "      🔍 <PERSON><PERSON> (<PERSON><PERSON> Gourd) - Medium: last_rec=07-08, days_since=2, cooldown=False\n", "      ✅ ALLOWED <PERSON><PERSON> (Bottle Gourd) - Medium (reason: no_cooldown)\n", "      🔍 Torai (Sponge Gourd): last_rec=07-08, days_since=2, cooldown=False\n", "      ✅ ALLOWED Torai (Sponge Gourd) (reason: no_cooldown)\n", "   🎯 Final recommendations: ['<PERSON><PERSON>', 'Tomato Hybrid', '<PERSON><PERSON> (Bottle Gourd) - Medium', '<PERSON><PERSON> (Sponge Gourd)']\n", "   📊 Blocked 0 items, generated 4 final recommendations\n", "   📊 Computing metrics using name...\n", "   👥 Users: 453 | P@k: 0.272 | R@k: 0.322 | Med k: 6\n", "   ⏱️ Day completed in 7.7s\n", "\n", "======================================================================\n", "🏆 ULTRA-FAST EVALUATION COMPLETED IN 59.8 SECONDS!\n", "🔍 NOW USING NAME FOR RECOMMENDATION MATCHING!\n", "======================================================================\n", "📊 Days evaluated: 7\n", "📊 User evaluations: 3,099\n", "📊 Product name column used: name\n", "📊 Personalized k values used: median=6, mean=6.6, range=3-30\n", "\n", "🎯 FINAL HIGH-RECALL RESULTS @ PERSONALIZED k (NAME MATCHING):\n", "   Precision@k: 0.2580\n", "   Recall@k: 0.3070\n", "   F1@k: 0.2534\n", "   Hit Rate: 0.7573\n", "   ⚠️  Still below 80% target. Current: 30.7%\n", "======================================================================\n", "\n", "✅ Complete setup and evaluation completed in 60.31 seconds\n", "\n", "💾 Saving results...\n", "\n", "📊 RECALL PERFORMANCE BY USER BUCKET:\n", "============================================================\n", "User Bucket Performance Summary:\n", "------------------------------------------------------------\n", "\n", "REPURCHASE Users (repurchase ratio ≥ 0.8):\n", "  Count: 1416 users\n", "  Mean Recall@k: 0.3359 (±0.2619)\n", "  Median Recall@k: 0.3333\n", "  Mean Precision@k: 0.2884\n", "  Hit Rate: 0.8044\n", "  Avg k value: 6.9\n", "  Avg repurchase ratio: 0.841\n", "\n", "BALANCED Users (repurchase ratio ≥ 0.5):\n", "  Count: 1683 users\n", "  Mean Recall@k: 0.2827 (±0.2728)\n", "  Median Recall@k: 0.2500\n", "  Mean Precision@k: 0.2324\n", "  Hit Rate: 0.7178\n", "  Avg k value: 6.4\n", "  Avg repurchase ratio: 0.739\n", "\n", "🏆 Best performing bucket: REPURCHASE (recall: 0.3359)\n", "⚠️  Worst performing bucket: BALANCED (recall: 0.2827)\n", "============================================================\n", "\n", "📊 PERSONALIZED FILTERING SUMMARY:\n", "============================================================\n", "Product name column used: name\n", "Total users: 1,110\n", "Total personalized recommendations: 59,682\n", "Average recommendations per user: 53.8\n", "\n", "Personalized k value distribution:\n", "  Median k: 9\n", "  Mean k: 10.5\n", "  Range: 3 - 37\n", "  Most common k values: {6: 6594, 7: 6419, 5: 5180, 8: 4928, 9: 4536}\n", "\n", "User type breakdown:\n", "  Moderate: 37,142 recommendations (threshold ≥ 0.1)\n", "  Conservative: 22,540 recommendations (threshold ≥ 0.15)\n", "\n", "Purchase probability distribution:\n", "  High confidence (≥0.6): 58,144\n", "  Medium confidence (0.4-0.59): 1,538\n", "  Low confidence (0.3-0.39): 0\n", "\n", "📋 Sample recommendations by user type:\n", "\n", "  Conservative user examples:\n", "    • Beetroot (Prob: 0.830, Type: repurchase)\n", "    • Amla (Prob: 0.808, Type: repurchase)\n", "    • Mint Leaves (Pudina) (Prob: 0.820, Type: repurchase)\n", "\n", "  Moderate user examples:\n", "    • Lemon (Prob: 0.793, Type: repurchase)\n", "    • Cucumber English (Prob: 0.789, Type: repurchase)\n", "    • Coriander (Dhania) (Prob: 0.784, Type: repurchase)\n", "✅ Recall distribution plot created (n=3099 evaluations)\n", "✅ Recommendation score distribution plot created (n=59682 recommendations)\n"]}], "source": ["# # -------------------------------------------------------------------------------- NOTEBOOK-CELL: CODE\n", "# # Required imports\n", "\n", "# from utils.notebookhelpers.helpers import Helpers\n", "# from utils.dtos.templateOutputCollection import TemplateOutputCollection\n", "# from utils.dtos.templateOutput import TemplateOutput\n", "# from utils.dtos.templateOutput import OutputType\n", "# from utils.dtos.templateOutput import ChartType\n", "# from utils.dtos.variable import Metadata\n", "# from utils.rcclient.commons.variable_datatype import VariableDatatype\n", "# from utils.dtos.templateOutput import FileType\n", "# from utils.dtos.rc_ml_model import RCMLModel\n", "# from utils.notebookhelpers.helpers import Helpers\n", "# from utils.libutils.vectorStores.utils import VectorStoreUtils\n", "\n", "# context = Helpers.getOrCreateContext(contextId='contextId', localVars=locals())\n", "\n", "# \n", "import pandas as pd\n", "import numpy as np\n", "from collections import defaultdict, Counter\n", "from datetime import datetime, timedelta\n", "import time\n", "import hashlib\n", "import matplotlib.pyplot as plt\n", "from matplotlib.patches import Rectangle\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "\n", "\n", "# from utils.notebookhelpers.helpers import Helpers\n", "# context = Helpers.getOrCreateContext(contextId='contextId', localVars=locals())\n", "# catalogue_rc = Helpers.getEntityData(context, 'catalogue_rc_with_parent_names')\n", "# customer_orders_rc = Helpers.getEntityData(context, 'HighFrequencyCustomerOrders')\n", "# repurchase_ratios_df = Helpers.getEntityData(context, 'repurchase_ratios')\n", "catalogue_rc = pd.read_csv('catalogue_rc_with_parent_names.csv')\n", "customer_orders_rc = pd.read_csv('customer_orders.csv')\n", "repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')\n", "\n", "class UltraOptimizedRecommendationSystem:\n", "    def __init__(self, user_repurchase_ratios_df=None, product_name_column='name'):\n", "        \"\"\"\n", "        Initialize the recommendation system with configurable product name column\n", "\n", "        Args:\n", "            user_repurchase_ratios_df: DataFrame with user repurchase ratios\n", "            product_name_column: Column name to use for product identification ('name' or 'sku_parent_name')\n", "        \"\"\"\n", "        # Configuration\n", "        self.product_name_column = product_name_column\n", "        print(f\"🔧 Using '{product_name_column}' column for product identification\")\n", "\n", "        # Pre-computed static data (computed once) - now using configurable product names\n", "        self.item_categories = {}\n", "        self.item_purchase_frequency = {}\n", "        self.item_avg_quantities = {}\n", "        self.catalogue_lookup = {}\n", "        self.name_to_sku_mapping = {}  # Map product names to available SKU codes\n", "        self.sku_to_name_mapping = {}  # Map SKU codes to product names\n", "\n", "        # Smart caching system\n", "        self.cache = {}\n", "        self.popular_items = []\n", "\n", "        # User-specific repurchase ratios\n", "        self.user_repurchase_ratios = {}\n", "        self.default_repurchase_ratio = 0.7  # Fallback for users not in the dataset\n", "\n", "        # 🆕 INTELLIGENT DEDUPLICATION SYSTEM\n", "        self.user_daily_essentials = {}  # {user_id: {item: daily_frequency_score}}\n", "        self.user_last_recommended = {}  # {user_id: {item: last_recommended_date}}\n", "        self.recommendation_cooldown_days = 2  # Cooldown period: items can't be recommended for 2 days\n", "        self.daily_essential_threshold = 0.5  # Reduced threshold (50% instead of 80% for more flexibility)\n", "        self.analysis_window_days = 14  # Analyze last 14 days for daily patterns\n", "        self.deduplication_enabled = True  # Flag to enable/disable deduplication for testing\n", "\n", "        if user_repurchase_ratios_df is not None:\n", "            self.load_user_repurchase_ratios(user_repurchase_ratios_df)\n", "    \n", "    def load_user_repurchase_ratios(self, repurchase_ratios_df):\n", "        \"\"\"Load user-specific repurchase ratios from DataFrame\"\"\"\n", "        self.user_repurchase_ratios = dict(zip(\n", "            repurchase_ratios_df['customer_id'], \n", "            repurchase_ratios_df['repurchase_ratio']\n", "        ))\n", "        print(f\"✅ Loaded dynamic repurchase ratios for {len(self.user_repurchase_ratios):,} users\")\n", "        if len(self.user_repurchase_ratios) > 0:\n", "            avg_ratio = repurchase_ratios_df['repurchase_ratio'].mean()\n", "            print(f\"   Average repurchase ratio: {avg_ratio:.3f}\")\n", "    \n", "    def get_user_repurchase_ratio(self, user_id):\n", "        \"\"\"Get repurchase ratio for a specific user\"\"\"\n", "        return self.user_repurchase_ratios.get(user_id, self.default_repurchase_ratio)\n", "\n", "    def analyze_daily_purchase_patterns(self, customer_orders_df, current_date):\n", "        \"\"\"\n", "        🧠 INTELLIGENT DAILY PURCHASE PATTERN ANALYSIS\n", "\n", "        Analyzes each user's historical purchasing patterns to identify:\n", "        1. Daily essential items (purchased 80%+ of days in analysis window)\n", "        2. Purchase frequency patterns for each user-item pair\n", "        3. Recommendation cooldown tracking\n", "\n", "        Args:\n", "            customer_orders_df: DataFrame with customer orders\n", "            current_date: Current evaluation date for analysis\n", "        \"\"\"\n", "        print(f\"🧠 Analyzing daily purchase patterns for {current_date.strftime('%Y-%m-%d')}...\")\n", "\n", "        # Convert to datetime if needed\n", "        if not pd.api.types.is_datetime64_any_dtype(customer_orders_df['delivery_date']):\n", "            customer_orders_df = customer_orders_df.copy()\n", "            customer_orders_df['delivery_date'] = pd.to_datetime(customer_orders_df['delivery_date'])\n", "\n", "        # Define analysis window (last N days before current date)\n", "        analysis_start_date = current_date - timedelta(days=self.analysis_window_days)\n", "\n", "        # Filter orders within analysis window\n", "        analysis_orders = customer_orders_df[\n", "            (customer_orders_df['delivery_date'] >= analysis_start_date) &\n", "            (customer_orders_df['delivery_date'] < current_date)\n", "        ].copy()\n", "\n", "        if len(analysis_orders) == 0:\n", "            print(f\"   ⚠️ No orders found in analysis window ({analysis_start_date} to {current_date})\")\n", "            return\n", "\n", "        # Convert to product names\n", "        analysis_orders['product_name'] = analysis_orders['sku_code'].map(self.sku_to_name_mapping)\n", "        analysis_orders = analysis_orders.dropna(subset=['product_name'])\n", "\n", "        print(f\"   📊 Analyzing {len(analysis_orders)} orders from {analysis_start_date.strftime('%Y-%m-%d')} to {current_date.strftime('%Y-%m-%d')}\")\n", "\n", "        # Create date range for analysis\n", "        date_range = pd.date_range(start=analysis_start_date, end=current_date - timedelta(days=1), freq='D')\n", "\n", "        # Analyze each user's purchase patterns\n", "        daily_essentials_found = 0\n", "        users_analyzed = 0\n", "\n", "        for user_id in analysis_orders['customer_id'].unique():\n", "            user_orders = analysis_orders[analysis_orders['customer_id'] == user_id]\n", "\n", "            # Create user's daily purchase matrix\n", "            user_daily_purchases = {}\n", "            for date in date_range:\n", "                date_orders = user_orders[user_orders['delivery_date'] == date]\n", "                purchased_items = set(date_orders['product_name']) if len(date_orders) > 0 else set()\n", "                user_daily_purchases[date] = purchased_items\n", "\n", "            # Calculate purchase frequency for each item\n", "            user_item_frequencies = {}\n", "            for product_name in user_orders['product_name'].unique():\n", "                # Count how many days this item was purchased\n", "                purchase_days = sum(1 for date_items in user_daily_purchases.values() if product_name in date_items)\n", "                frequency_score = purchase_days / len(date_range)\n", "                user_item_frequencies[product_name] = frequency_score\n", "\n", "            # Identify daily essentials (items purchased frequently)\n", "            daily_essentials = {\n", "                item: freq for item, freq in user_item_frequencies.items()\n", "                if freq >= self.daily_essential_threshold\n", "            }\n", "\n", "            # Store results\n", "            self.user_daily_essentials[user_id] = daily_essentials\n", "\n", "            if daily_essentials:\n", "                daily_essentials_found += len(daily_essentials)\n", "\n", "            users_analyzed += 1\n", "\n", "        print(f\"   ✅ Analyzed {users_analyzed} users\")\n", "        print(f\"   🎯 Found {daily_essentials_found} daily essential items across all users\")\n", "        print(f\"   📈 Average daily essentials per user: {daily_essentials_found/users_analyzed:.1f}\")\n", "\n", "        # Show sample daily essentials\n", "        sample_users = list(self.user_daily_essentials.keys())[:3]\n", "        for user_id in sample_users:\n", "            essentials = self.user_daily_essentials[user_id]\n", "            if essentials:\n", "                print(f\"   👤 Sample user {user_id[:8]}...: {len(essentials)} daily essentials\")\n", "                for item, freq in list(essentials.items())[:3]:\n", "                    print(f\"      • {item}: {freq:.1%} purchase frequency\")\n", "\n", "    def is_item_in_cooldown(self, user_id, item_name, current_date):\n", "        \"\"\"\n", "        Check if an item is in recommendation cooldown period for a user\n", "\n", "        Args:\n", "            user_id: User ID\n", "            item_name: Product name\n", "            current_date: Current evaluation date\n", "\n", "        Returns:\n", "            bool: True if item is in cooldown, False otherwise\n", "        \"\"\"\n", "        if user_id not in self.user_last_recommended:\n", "            return False\n", "\n", "        if item_name not in self.user_last_recommended[user_id]:\n", "            return False\n", "\n", "        last_recommended = self.user_last_recommended[user_id][item_name]\n", "        days_since_recommended = (current_date - last_recommended).days\n", "\n", "        return days_since_recommended < self.recommendation_cooldown_days\n", "\n", "    def is_daily_essential(self, user_id, item_name):\n", "        \"\"\"\n", "        Check if an item is classified as a daily essential for a user\n", "\n", "        Args:\n", "            user_id: User ID\n", "            item_name: Product name\n", "\n", "        Returns:\n", "            bool: True if item is a daily essential, False otherwise\n", "        \"\"\"\n", "        if user_id not in self.user_daily_essentials:\n", "            return False\n", "\n", "        return item_name in self.user_daily_essentials[user_id]\n", "\n", "    def update_recommendation_tracking(self, user_id, item_name, recommendation_date):\n", "        \"\"\"\n", "        Update the last recommended date for a user-item pair\n", "\n", "        Args:\n", "            user_id: User ID\n", "            item_name: Product name\n", "            recommendation_date: Date when item was recommended\n", "        \"\"\"\n", "        if user_id not in self.user_last_recommended:\n", "            self.user_last_recommended[user_id] = {}\n", "\n", "        self.user_last_recommended[user_id][item_name] = recommendation_date\n", "\n", "    def get_alternative_recommendations(self, user_id, blocked_item, user_profile, similarity_dict, num_alternatives=3):\n", "        \"\"\"\n", "        🔄 INTELLIGENT ALTERNATIVE RECOMMENDATION STRATEGIES\n", "\n", "        When an item is blocked due to cooldown, find intelligent alternatives:\n", "        1. Similar items from the same category\n", "        2. Complementary items that pair well with recent purchases\n", "        3. Discovery items from frequently shopped categories\n", "        4. Collaborative filtering alternatives\n", "\n", "        Args:\n", "            user_id: User ID\n", "            blocked_item: Item that's blocked due to cooldown\n", "            user_profile: User's purchase profile\n", "            similarity_dict: Item similarity matrix\n", "            num_alternatives: Number of alternatives to generate\n", "\n", "        Returns:\n", "            list: Alternative recommendation items\n", "        \"\"\"\n", "        alternatives = []\n", "        blocked_category = self.item_categories.get(blocked_item, 'Unknown')\n", "\n", "        # Strategy 1: Similar items from same category\n", "        if blocked_category != 'Unknown':\n", "            category_items = [\n", "                item for item, category in self.item_categories.items()\n", "                if category == blocked_category and item != blocked_item\n", "                and item not in user_profile.get('purchased_items', [])\n", "                and not self.is_item_in_cooldown(user_id, item, pd.Timestamp.now())\n", "            ]\n", "\n", "            # Sort by popularity and take top candidates\n", "            category_alternatives = sorted(\n", "                category_items,\n", "                key=lambda x: self.item_purchase_frequency.get(x, 0),\n", "                reverse=True\n", "            )[:num_alternatives]\n", "\n", "            alternatives.extend(category_alternatives)\n", "\n", "        # Strategy 2: Collaborative filtering alternatives\n", "        if blocked_item in similarity_dict:\n", "            similar_items = [\n", "                item for item, sim_score in similarity_dict[blocked_item].items()\n", "                if item not in user_profile.get('purchased_items', [])\n", "                and not self.is_item_in_cooldown(user_id, item, pd.Timestamp.now())\n", "                and sim_score > 0.1  # Minimum similarity threshold\n", "            ]\n", "\n", "            # Sort by similarity score\n", "            collab_alternatives = sorted(\n", "                similar_items,\n", "                key=lambda x: similarity_dict[blocked_item].get(x, 0),\n", "                reverse=True\n", "            )[:num_alternatives]\n", "\n", "            alternatives.extend(collab_alternatives)\n", "\n", "        # Strategy 3: Discovery items from user's preferred categories\n", "        user_categories = user_profile.get('category_preferences', {})\n", "        for category, preference in sorted(user_categories.items(), key=lambda x: x[1], reverse=True)[:3]:\n", "            if preference > 0.1:  # User shows interest in this category\n", "                category_discovery = [\n", "                    item for item in self.popular_items\n", "                    if self.item_categories.get(item) == category\n", "                    and item not in user_profile.get('purchased_items', [])\n", "                    and not self.is_item_in_cooldown(user_id, item, pd.Timestamp.now())\n", "                ][:2]  # Take top 2 from each preferred category\n", "\n", "                alternatives.extend(category_discovery)\n", "\n", "        # Remove duplicates and limit to requested number\n", "        unique_alternatives = []\n", "        seen = set()\n", "        for item in alternatives:\n", "            if item not in seen:\n", "                unique_alternatives.append(item)\n", "                seen.add(item)\n", "                if len(unique_alternatives) >= num_alternatives:\n", "                    break\n", "\n", "        return unique_alternatives[:num_alternatives]\n", "\n", "    def apply_intelligent_deduplication(self, recommendations, user_id, current_date, user_profile, similarity_dict):\n", "        \"\"\"\n", "        🎯 INTELLIGENT RECOMMENDATION DEDUPLICATION (BALANCED APPROACH)\n", "\n", "        Applies smart deduplication logic with safeguards:\n", "        1. Allow daily essentials even if recently recommended\n", "        2. Apply cooldown only to top-scored non-essential items (to maintain diversity)\n", "        3. Replace blocked items with intelligent alternatives\n", "        4. Ensure minimum recommendation count is maintained\n", "\n", "        Args:\n", "            recommendations: List of recommendation dictionaries\n", "            user_id: User ID\n", "            current_date: Current evaluation date\n", "            user_profile: User's purchase profile\n", "            similarity_dict: Item similarity matrix\n", "\n", "        Returns:\n", "            list: Deduplicated and enhanced recommendations\n", "        \"\"\"\n", "        if not self.deduplication_enabled:\n", "            # If deduplication is disabled, just update tracking and return original\n", "            for rec in recommendations:\n", "                self.update_recommendation_tracking(user_id, rec['product_name'], current_date)\n", "            return recommendations\n", "\n", "        # Debug logging for specific problematic users\n", "        debug_user = user_id.startswith('570fa519') or user_id.startswith('4bcb4707')\n", "        if debug_user:\n", "            print(f\"🔍 DEBUG: Deduplication for user {user_id[:8]}... on {current_date.strftime('%Y-%m-%d')}\")\n", "            print(f\"   📝 Input recommendations: {[r['product_name'] for r in recommendations[:5]]}\")\n", "\n", "        deduplicated_recs = []\n", "        blocked_items = []\n", "\n", "        # Sort recommendations by score to prioritize high-scoring items\n", "        sorted_recs = sorted(recommendations, key=lambda x: x['score'], reverse=True)\n", "\n", "        for rec in sorted_recs:\n", "            item_name = rec['product_name']\n", "\n", "            # Check if item is a daily essential\n", "            is_essential = self.is_daily_essential(user_id, item_name)\n", "\n", "            # Check if item is in cooldown\n", "            in_cooldown = self.is_item_in_cooldown(user_id, item_name, current_date)\n", "\n", "            # Debug logging for cooldown checks\n", "            if debug_user and user_id in self.user_last_recommended and item_name in self.user_last_recommended[user_id]:\n", "                last_rec_date = self.user_last_recommended[user_id][item_name]\n", "                days_since = (current_date - last_rec_date).days\n", "                print(f\"      🔍 {item_name}: last_rec={last_rec_date.strftime('%m-%d')}, days_since={days_since}, cooldown={in_cooldown}\")\n", "\n", "            # IMPROVED DEDUPLICATION LOGIC:\n", "            # 1. Always allow daily essentials\n", "            # 2. For non-essentials, apply cooldown but ensure minimum recommendations\n", "            # 3. Only bypass cooldown if we have very few recommendations left\n", "\n", "            # Calculate minimum recommendations needed (at least 50% of target)\n", "            min_recommendations_needed = max(2, len(sorted_recs) * 0.5)\n", "\n", "            should_allow = (\n", "                is_essential or  # Always allow daily essentials\n", "                not in_cooldown or  # Allow if not in cooldown\n", "                len(deduplicated_recs) < min_recommendations_needed  # Only bypass cooldown if we really need more recommendations\n", "            )\n", "\n", "            if should_allow:\n", "                # Allow the recommendation\n", "                deduplicated_recs.append(rec)\n", "                # Update tracking\n", "                self.update_recommendation_tracking(user_id, item_name, current_date)\n", "\n", "                if debug_user:\n", "                    reason = \"essential\" if is_essential else (\"no_cooldown\" if not in_cooldown else \"min_recs_needed\")\n", "                    print(f\"      ✅ ALLOWED {item_name} (reason: {reason})\")\n", "            else:\n", "                # Block the recommendation and try to find alternatives\n", "                blocked_items.append((item_name, rec))\n", "\n", "                if debug_user:\n", "                    print(f\"      ❌ BLOCKED {item_name} (in cooldown, not essential, have enough recs)\")\n", "\n", "        # Generate alternatives for blocked items (but only if we have room)\n", "        max_alternatives = min(len(blocked_items), max(1, len(recommendations) - len(deduplicated_recs)))\n", "\n", "        for i, (blocked_item, original_rec) in enumerate(blocked_items[:max_alternatives]):\n", "            alternatives = self.get_alternative_recommendations(\n", "                user_id, blocked_item, user_profile, similarity_dict, num_alternatives=2\n", "            )\n", "\n", "            if alternatives:\n", "                # Use the first alternative\n", "                alt_item = alternatives[0]\n", "                # Create alternative recommendation with similar score to original\n", "                alt_rec = {\n", "                    'sku_code': self.catalogue_lookup.get(alt_item, {}).get('sku_codes', ['UNKNOWN'])[0],\n", "                    'product_name': alt_item,\n", "                    'category': self.item_categories.get(alt_item, 'Unknown'),\n", "                    'score': max(0.4, original_rec['score'] * 0.8),  # Slightly lower score than original\n", "                    'predicted_quantity': self.item_avg_quantities.get(alt_item, 1.0),\n", "                    'recommendation_type': 'alternative',\n", "                    'strategy': 'intelligent_deduplication',\n", "                    'replaced_item': blocked_item\n", "                }\n", "\n", "                deduplicated_recs.append(alt_rec)\n", "                # Update tracking for alternative\n", "                self.update_recommendation_tracking(user_id, alt_item, current_date)\n", "            else:\n", "                # If no alternatives found, keep the original (better than having too few recommendations)\n", "                deduplicated_recs.append(original_rec)\n", "                self.update_recommendation_tracking(user_id, blocked_item, current_date)\n", "\n", "        if debug_user:\n", "            final_items = [r['product_name'] for r in deduplicated_recs]\n", "            print(f\"   🎯 Final recommendations: {final_items[:5]}\")\n", "            print(f\"   📊 Blocked {len(blocked_items)} items, generated {len(deduplicated_recs)} final recommendations\")\n", "\n", "        return deduplicated_recs\n", "    \n", "    def calculate_purchase_probability(self, recommendation, user_repurchase_ratio, user_id):\n", "        \"\"\"\n", "        Calculate purchase probability based on recommendation context\n", "        \n", "        Args:\n", "            recommendation: Dict containing recommendation details\n", "            user_repurchase_ratio: User's repurchase ratio\n", "            user_id: User ID\n", "            \n", "        Returns:\n", "            float: Purchase probability between 0 and 1\n", "        \"\"\"\n", "        product_name = recommendation['product_name']\n", "        rec_score = recommendation['score']\n", "        rec_type = recommendation['recommendation_type']\n", "        \n", "        # Get item's global popularity (now using configurable product names)\n", "        item_popularity = self.item_purchase_frequency.get(product_name, 0.01)\n", "        \n", "        if rec_type == 'repurchase':\n", "            # Higher base probability for repurchase items since user bought before\n", "            # Base probability starts higher and is boosted by recommendation score\n", "            base_prob = 0.6 + (0.3 * rec_score)  # Range: 0.6 to 0.9\n", "            \n", "            # Adjust by user's repurchase behavior\n", "            purchase_probability = base_prob * user_repurchase_ratio\n", "            \n", "            # Small boost for popular items (items others frequently buy)\n", "            popularity_boost = min(0.1, item_popularity * 2)  # Cap at 0.1\n", "            purchase_probability += popularity_boost\n", "            \n", "        else:  # discovery items\n", "            # Lower base probability for new items user hasn't purchased before\n", "            # Base probability is more dependent on recommendation score\n", "            base_prob = 0.2 + (0.4 * rec_score)  # Range: 0.2 to 0.6\n", "            \n", "            # Boost by item's global popularity (popular items more likely to be purchased)\n", "            popularity_factor = 1 + (item_popularity * 3)  # Popularity has more impact for discovery\n", "            purchase_probability = base_prob * popularity_factor\n", "            \n", "            # Slight adjustment based on user's openness to new items\n", "            # Users with higher repurchase ratios might be less open to new items\n", "            discovery_openness = 2 - user_repurchase_ratio  # Range: 1 to 1.3\n", "            purchase_probability *= discovery_openness\n", "        \n", "        # Ensure probability is between 0 and 1\n", "        purchase_probability = max(0.0, min(1.0, purchase_probability))\n", "        \n", "        return round(purchase_probability, 4)\n", "    \n", "    def filter_recommendations_personalized(self, recommendations_df):\n", "        \"\"\"\n", "        Filter recommendations DataFrame using personalized thresholds based on user repurchase ratios\n", "        \n", "        Args:\n", "            recommendations_df: DataFrame with recommendations containing 'customer_id', 'purchase_probability', \n", "                              and 'user_repurchase_ratio' columns\n", "            \n", "        Returns:\n", "            DataFrame: Filtered recommendations that meet personalized thresholds\n", "        \"\"\"\n", "        if recommendations_df.empty:\n", "            return recommendations_df\n", "        \n", "        # Create a copy to avoid modifying the original\n", "        filtered_df = recommendations_df.copy()\n", "        \n", "        # Add personalized threshold and user type columns\n", "        def get_personalized_info(user_repurchase_ratio):\n", "            if user_repurchase_ratio >= 0.8:\n", "                return 0.6, 'Conservative'\n", "            elif user_repurchase_ratio >= 0.5:\n", "                return 0.45, 'Moderate'\n", "            else:\n", "                return 0.3, 'Exploratory'\n", "        \n", "        # Apply personalized logic\n", "        filtered_df[['personalized_threshold', 'user_type']] = filtered_df['user_repurchase_ratio'].apply(\n", "            lambda x: pd.Series(get_personalized_info(x))\n", "        )\n", "        \n", "        # Filter based on personalized threshold\n", "        filtered_df = filtered_df[filtered_df['purchase_probability'] >= filtered_df['personalized_threshold']]\n", "        \n", "        # Update show_recommendation to True for filtered items\n", "        filtered_df['show_recommendation'] = True\n", "        \n", "        print(f\"✅ Personalized filtering applied:\")\n", "        print(f\"   Original recommendations: {len(recommendations_df):,}\")\n", "        print(f\"   Filtered recommendations: {len(filtered_df):,}\")\n", "        print(f\"   Filtering efficiency: {len(filtered_df)/len(recommendations_df)*100:.1f}%\")\n", "        \n", "        # Show breakdown by user type\n", "        user_type_counts = filtered_df['user_type'].value_counts()\n", "        print(f\"   User type breakdown:\")\n", "        for user_type, count in user_type_counts.items():\n", "            avg_threshold = filtered_df[filtered_df['user_type'] == user_type]['personalized_threshold'].iloc[0]\n", "            print(f\"     {user_type}: {count:,} recommendations (threshold: {avg_threshold})\")\n", "        \n", "        return filtered_df\n", "    \n", "    def get_selection_recommendations(self, recommendations_df, user_id, strategy='tiered'):\n", "        \"\"\"\n", "        Provide selection guidance based on purchase probabilities\n", "        \n", "        Args:\n", "            recommendations_df: DataFrame with recommendations\n", "            user_id: User ID to filter recommendations\n", "            strategy: Selection strategy ('tiered', 'type_based', 'top_n', 'personalized')\n", "            \n", "        Returns:\n", "            dict: Selection recommendations with explanations\n", "        \"\"\"\n", "        user_recs = recommendations_df[recommendations_df['customer_id'] == user_id].copy()\n", "        \n", "        if len(user_recs) == 0:\n", "            return {'message': 'No recommendations found for this user'}\n", "        \n", "        user_repurchase_ratio = user_recs['user_repurchase_ratio'].iloc[0]\n", "        \n", "        if strategy == 'tiered':\n", "            high_confidence = user_recs[user_recs['purchase_probability'] >= 0.7]\n", "            medium_confidence = user_recs[(user_recs['purchase_probability'] >= 0.4) & \n", "                                        (user_recs['purchase_probability'] < 0.7)]\n", "            low_confidence = user_recs[user_recs['purchase_probability'] < 0.4]\n", "            \n", "            return {\n", "                'strategy': 'Tiered Confidence',\n", "                'high_confidence': {\n", "                    'count': len(high_confidence),\n", "                    'items': high_confidence[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),\n", "                    'recommendation': 'Strong buy - high likelihood of purchase'\n", "                },\n", "                'medium_confidence': {\n", "                    'count': len(medium_confidence),\n", "                    'items': medium_confidence[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),\n", "                    'recommendation': 'Consider adding - moderate likelihood'\n", "                },\n", "                'low_confidence': {\n", "                    'count': len(low_confidence),\n", "                    'items': low_confidence[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),\n", "                    'recommendation': 'Explore option - lower likelihood but might discover something new'\n", "                }\n", "            }\n", "        \n", "        elif strategy == 'type_based':\n", "            repurchase_items = user_recs[user_recs['recommendation_type'] == 'repurchase']\n", "            discovery_items = user_recs[user_recs['recommendation_type'] == 'discovery']\n", "            \n", "            # Different thresholds for different types\n", "            strong_repurchase = repurchase_items[repurchase_items['purchase_probability'] >= 0.6]\n", "            good_discovery = discovery_items[discovery_items['purchase_probability'] >= 0.35]\n", "            \n", "            return {\n", "                'strategy': 'Type-Based Selection',\n", "                'repurchase_recommendations': {\n", "                    'count': len(strong_repurchase),\n", "                    'items': strong_repurchase[['product_name', 'purchase_probability']].to_dict('records'),\n", "                    'threshold': 0.6,\n", "                    'explanation': 'Items you\\'ve bought before with high repurchase probability'\n", "                },\n", "                'discovery_recommendations': {\n", "                    'count': len(good_discovery),\n", "                    'items': good_discovery[['product_name', 'purchase_probability']].to_dict('records'),\n", "                    'threshold': 0.35,\n", "                    'explanation': 'New items with good discovery potential'\n", "                }\n", "            }\n", "        \n", "        elif strategy == 'top_n':\n", "            # Top 5-7 items with minimum threshold\n", "            min_threshold = 0.3\n", "            qualified_items = user_recs[user_recs['purchase_probability'] >= min_threshold]\n", "            top_items = qualified_items.nlargest(7, 'purchase_probability')\n", "            \n", "            return {\n", "                'strategy': 'Top-N with Mini<PERSON> Threshold',\n", "                'selected_items': {\n", "                    'count': len(top_items),\n", "                    'items': top_items[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),\n", "                    'explanation': f'Top {len(top_items)} items with probability >= {min_threshold}'\n", "                }\n", "            }\n", "        \n", "        elif strategy == 'personalized':\n", "            # Adjust thresholds based on user behavior\n", "            if user_repurchase_ratio >= 0.8:  # Conservative user\n", "                threshold = 0.6\n", "                user_type = 'Conservative'\n", "            elif user_repurchase_ratio >= 0.5:  # Moderate user\n", "                threshold = 0.45\n", "                user_type = 'Moderate'\n", "            else:  # Exploratory user\n", "                threshold = 0.3\n", "                user_type = 'Exploratory'\n", "            \n", "            selected_items = user_recs[user_recs['purchase_probability'] >= threshold]\n", "            \n", "            return {\n", "                'strategy': 'Personalized Threshold',\n", "                'user_type': user_type,\n", "                'user_repurchase_ratio': user_repurchase_ratio,\n", "                'threshold': threshold,\n", "                'selected_items': {\n", "                    'count': len(selected_items),\n", "                    'items': selected_items[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),\n", "                    'explanation': f'Items selected based on your {user_type.lower()} shopping pattern'\n", "                }\n", "            }\n", "        \n", "        else:\n", "            return {'error': 'Invalid strategy. Use: tiered, type_based, top_n, or personalized'}\n", "        \n", "    def precompute_static_features(self, customer_orders_df, catalogue_df):\n", "        \"\"\"Pre-compute all static features that don't change during evaluation - now using configurable product names\"\"\"\n", "        print(f\"🔧 Pre-computing static features using '{self.product_name_column}' column (one-time setup)...\")\n", "        \n", "        # Validate that the specified column exists\n", "        if self.product_name_column not in catalogue_df.columns:\n", "            raise ValueError(f\"Column '{self.product_name_column}' not found in catalogue. Available columns: {list(catalogue_df.columns)}\")\n", "        \n", "        # Create clean catalogue with unique names (remove duplicates based on chosen column)\n", "        clean_catalogue = catalogue_df.drop_duplicates(subset=[self.product_name_column]).copy()\n", "        \n", "        # Create mappings between names and SKU codes\n", "        print(f\"📊 Creating {self.product_name_column}-to-SKU mappings...\")\n", "        for _, row in catalogue_df.iterrows():\n", "            product_name = row[self.product_name_column]\n", "            sku_code = row['sku_code']\n", "            \n", "            # Map SKU to name\n", "            self.sku_to_name_mapping[sku_code] = product_name\n", "            \n", "            # Map name to list of SKU codes (multiple SKUs can have same name)\n", "            if product_name not in self.name_to_sku_mapping:\n", "                self.name_to_sku_mapping[product_name] = []\n", "            self.name_to_sku_mapping[product_name].append(sku_code)\n", "        \n", "        # Convert customer orders to use product names\n", "        print(f\"📊 Converting orders to use {self.product_name_column}...\")\n", "        customer_orders_with_names = customer_orders_df.copy()\n", "        customer_orders_with_names['product_name'] = customer_orders_with_names['sku_code'].map(self.sku_to_name_mapping)\n", "        \n", "        # Remove any orders where we couldn't map the SKU to a name\n", "        customer_orders_with_names = customer_orders_with_names.dropna(subset=['product_name'])\n", "        \n", "        # Item categories (using configurable column as keys)\n", "        self.item_categories = dict(zip(clean_catalogue[self.product_name_column], clean_catalogue['category_name']))\n", "        \n", "        # Catalogue lookup (using configurable column as keys)\n", "        self.catalogue_lookup = {}\n", "        for _, row in clean_catalogue.iterrows():\n", "            product_name = row[self.product_name_column]\n", "            self.catalogue_lookup[product_name] = {\n", "                'name': product_name,\n", "                'category_name': row['category_name'],\n", "                'sku_codes': self.name_to_sku_mapping[product_name],  # List of all SKU codes for this product\n", "                'original_name': row.get('name', product_name),  # Keep original name for reference\n", "                'parent_name': row.get('sku_parent_name', product_name)  # Keep parent name for reference\n", "            }\n", "        \n", "        # Global item statistics (computed using product names)\n", "        total_orders = customer_orders_with_names['display_order_id'].nunique()\n", "        \n", "        # Group by product name and aggregate\n", "        name_counts = customer_orders_with_names.groupby('product_name').size()\n", "        self.item_purchase_frequency = (name_counts / total_orders).to_dict()\n", "        \n", "        # Average quantities by product name\n", "        self.item_avg_quantities = customer_orders_with_names.groupby('product_name')['ordered_qty'].mean().to_dict()\n", "        \n", "        # Popular items (products purchased by at least 3 users)\n", "        name_user_counts = customer_orders_with_names.groupby('product_name')['customer_id'].nunique()\n", "        self.popular_items = name_user_counts[name_user_counts >= 3].index.tolist()\n", "        \n", "        print(f\"✅ Pre-computed {len(self.item_categories)} product categories using '{self.product_name_column}'\")\n", "        print(f\"✅ Pre-computed {len(self.popular_items)} popular products\")\n", "        print(f\"✅ Pre-computed mappings: {len(self.name_to_sku_mapping)} {self.product_name_column} values → {sum(len(skus) for skus in self.name_to_sku_mapping.values())} SKUs\")\n", "        print(f\"✅ Pre-computed global statistics using {self.product_name_column}\")\n", "    \n", "    def build_matrices_ultra_fast(self, orders_df, eval_date=None):\n", "        \"\"\"Build matrices using ultra-fast vectorized operations - now using configurable product names\"\"\"\n", "\n", "        # Convert orders to use product names\n", "        orders_with_names = orders_df.copy()\n", "        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)\n", "        orders_with_names = orders_with_names.dropna(subset=['product_name'])\n", "\n", "        # Create data hash for caching - include eval_date to ensure different training sets get different hashes\n", "        eval_date_str = eval_date.strftime('%Y-%m-%d') if eval_date is not None else 'no_eval_date'\n", "        data_hash = hashlib.md5(f\"{len(orders_with_names)}_{orders_with_names['delivery_date'].min()}_{orders_with_names['delivery_date'].max()}_{self.product_name_column}_{eval_date_str}\".encode()).hexdigest()\n", "        \n", "        if data_hash in self.cache:\n", "            print(f\"✅ Using cached matrices for '{self.product_name_column}'\")\n", "            return self.cache[data_hash]\n", "        \n", "        print(f\"⚡ Building matrices (vectorized using {self.product_name_column})...\")\n", "        \n", "        # User-item frequency matrix (using configurable product names)\n", "        user_item_counts = orders_with_names.groupby(['customer_id', 'product_name']).size().reset_index(name='count')\n", "        user_item_matrix = user_item_counts.pivot(index='customer_id', columns='product_name', values='count').fillna(0)\n", "        \n", "        # User-item quantity matrix (using configurable product names)\n", "        user_item_qty = orders_with_names.groupby(['customer_id', 'product_name'])['ordered_qty'].mean().reset_index()\n", "        user_item_qty_matrix = user_item_qty.pivot(index='customer_id', columns='product_name', values='ordered_qty').fillna(0)\n", "        \n", "        # 🚀 AGGRESSIVE SIMILARITY MATRIX for HIGH RECALL\n", "        print(f\"⚡ Computing AGGRESSIVE similarity matrix for high recall using {self.product_name_column}...\")\n", "        popular_in_matrix = [item for item in self.popular_items if item in user_item_matrix.columns]\n", "        \n", "        similarity_dict = {}\n", "        if len(popular_in_matrix) > 0:\n", "            # Use simplified Jaccard on binary matrix\n", "            user_item_binary = (user_item_matrix[popular_in_matrix] > 0).astype(int)\n", "            \n", "            # MUCH MORE AGGRESSIVE similarity computation for high recall\n", "            for i, item1 in enumerate(popular_in_matrix[:200]):  # Doubled from 100 to 200\n", "                item1_users = user_item_binary[item1]\n", "                similarities = {}\n", "                \n", "                for item2 in popular_in_matrix[:200]:  # Consider more items\n", "                    if item1 != item2:\n", "                        item2_users = user_item_binary[item2]\n", "                        intersection = (item1_users & item2_users).sum()\n", "                        union = (item1_users | item2_users).sum()\n", "                        \n", "                        if union > 0 and intersection >= 1:  # LOWERED from 2 to 1\n", "                            jaccard_sim = intersection / union\n", "                            if jaccard_sim > 0.05:  # LOWERED from 0.1 to 0.05 for more connections\n", "                                similarities[item2] = jaccard_sim\n", "                \n", "                if similarities:\n", "                    # Keep top 25 instead of 10 for more recommendations\n", "                    top_similar = dict(sorted(similarities.items(), key=lambda x: x[1], reverse=True)[:25])\n", "                    similarity_dict[item1] = top_similar\n", "        \n", "        # Cache results\n", "        result = {\n", "            'user_item_matrix': user_item_matrix,\n", "            'user_item_qty_matrix': user_item_qty_matrix,\n", "            'similarity_dict': similarity_dict,\n", "            'popular_items': popular_in_matrix\n", "        }\n", "        self.cache[data_hash] = result\n", "        \n", "        print(f\"✅ Built matrices using {self.product_name_column}: {user_item_matrix.shape}, similarity: {len(similarity_dict)} items\")\n", "        return result\n", "    \n", "    def build_user_profiles_lightning_fast(self, user_item_matrix, user_item_qty_matrix):\n", "        \"\"\"Build user profiles with minimal computation - now using configurable product names\"\"\"\n", "        \n", "        print(f\"⚡ Building user profiles (lightning fast using {self.product_name_column})...\")\n", "        user_profiles = {}\n", "        \n", "        for user_id in user_item_matrix.index:\n", "            user_row = user_item_matrix.loc[user_id]\n", "            user_items = user_row[user_row > 0].index.tolist()\n", "            \n", "            if not user_items:\n", "                continue\n", "            \n", "            # Pre-computed data (no loops) - now using configurable product names\n", "            purchase_counts = user_row[user_items].to_dict()\n", "            \n", "            # Simplified quantities\n", "            quantities = {}\n", "            if user_id in user_item_qty_matrix.index:\n", "                qty_row = user_item_qty_matrix.loc[user_id]\n", "                quantities = {item: qty for item, qty in qty_row[user_items].items() if qty > 0}\n", "            \n", "            # Simplified category preferences (using configurable product names)\n", "            categories = defaultdict(int)\n", "            for product_name in user_items:\n", "                if product_name in self.item_categories:\n", "                    categories[self.item_categories[product_name]] += purchase_counts[product_name]\n", "            \n", "            # Normalize\n", "            total = sum(categories.values())\n", "            if total > 0:\n", "                categories = {cat: count/total for cat, count in categories.items()}\n", "            \n", "            user_profiles[user_id] = {\n", "                'items': user_items,  # Now contains configurable product names\n", "                'purchase_counts': purchase_counts,\n", "                'quantities': quantities,\n", "                'category_preferences': dict(categories),\n", "                'purchased_items': user_items  # Now contains configurable product names\n", "            }\n", "        \n", "        print(f\"✅ Built {len(user_profiles)} user profiles using {self.product_name_column}\")\n", "        return user_profiles\n", "    \n", "    def get_recommendations_ultra_fast(self, user_id, user_profile, similarity_dict, top_n=10, repurchase_ratio=None):\n", "        \"\"\"Ultra-fast recommendation generation with minimal computation - now using configurable product names\"\"\"\n", "        \n", "        # Use user-specific repurchase ratio if not provided\n", "        if repurchase_ratio is None:\n", "            repurchase_ratio = self.get_user_repurchase_ratio(user_id)\n", "        \n", "        purchased_items = set(user_profile['purchased_items'])  # Now contains configurable product names\n", "        repurchase_count = max(1, int(top_n * repurchase_ratio))\n", "        discovery_count = max(1, top_n - repurchase_count)\n", "        \n", "        # REPURCHASE RECOMMENDATIONS (simplified) - using configurable product names\n", "        repurchase_scores = {}\n", "        for product_name in purchased_items:\n", "            count = user_profile['purchase_counts'].get(product_name, 0)\n", "            # Simplified scoring for maximum speed\n", "            repurchase_scores[product_name] = count\n", "        \n", "        sorted_repurchase = sorted(repurchase_scores.items(), key=lambda x: x[1], reverse=True)\n", "        \n", "        # Normalize repurchase scores to 0-1 scale\n", "        repurchase_recs = []\n", "        if sorted_repurchase:\n", "            max_repurchase_score = sorted_repurchase[0][1] if sorted_repurchase else 1\n", "            min_repurchase_score = sorted_repurchase[-1][1] if len(sorted_repurchase) > 1 else 0\n", "            score_range = max_repurchase_score - min_repurchase_score\n", "            \n", "            for product_name, raw_score in sorted_repurchase[:repurchase_count]:\n", "                # Normalize score to 0-1 range\n", "                if score_range > 0:\n", "                    normalized_score = (raw_score - min_repurchase_score) / score_range\n", "                else:\n", "                    normalized_score = 1.0\n", "                \n", "                qty = user_profile['quantities'].get(product_name, self.item_avg_quantities.get(product_name, 1.0))\n", "                item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})\n", "                \n", "                # Select the first SKU code for this product name\n", "                sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'\n", "                \n", "                repurchase_recs.append({\n", "                    'sku_code': sku_code,\n", "                    'product_name': product_name,\n", "                    'category': item_info['category_name'],\n", "                    'score': round(normalized_score, 4),\n", "                    'predicted_quantity': round(qty, 3),\n", "                    'recommendation_type': 'repurchase'\n", "                })\n", "        \n", "        # DISCOVERY RECOMMENDATIONS (ultra-simplified) - using configurable product names\n", "        discovery_scores = defaultdict(float)\n", "        \n", "        # Content-based (only for items with similarities)\n", "        for product_name in purchased_items:\n", "            if product_name in similarity_dict:\n", "                count = user_profile['purchase_counts'].get(product_name, 0)\n", "                for similar_item, sim in similarity_dict[product_name].items():\n", "                    if similar_item not in purchased_items:\n", "                        discovery_scores[similar_item] += sim * count\n", "        \n", "        # Popular items in user's preferred categories\n", "        for product_name in self.popular_items[:200]:  # Only check top 200 popular items\n", "            if product_name not in purchased_items and product_name in self.item_categories:\n", "                category = self.item_categories[product_name]\n", "                if category in user_profile['category_preferences']:\n", "                    cat_pref = user_profile['category_preferences'][category]\n", "                    popularity = self.item_purchase_frequency.get(product_name, 0)\n", "                    discovery_scores[product_name] += cat_pref * popularity * 100  # Scale factor\n", "        \n", "        sorted_discovery = sorted(discovery_scores.items(), key=lambda x: x[1], reverse=True)\n", "        \n", "        # Normalize discovery scores to 0-1 scale\n", "        discovery_recs = []\n", "        if sorted_discovery:\n", "            max_discovery_score = sorted_discovery[0][1] if sorted_discovery else 1\n", "            min_discovery_score = sorted_discovery[-1][1] if len(sorted_discovery) > 1 else 0\n", "            score_range = max_discovery_score - min_discovery_score\n", "            \n", "            for product_name, raw_score in sorted_discovery[:discovery_count]:\n", "                # Normalize score to 0-1 range\n", "                if score_range > 0:\n", "                    normalized_score = (raw_score - min_discovery_score) / score_range\n", "                else:\n", "                    normalized_score = 1.0\n", "                \n", "                qty = self.item_avg_quantities.get(product_name, 1.0)\n", "                item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})\n", "                \n", "                # Select the first SKU code for this product name\n", "                sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'\n", "                \n", "                discovery_recs.append({\n", "                    'sku_code': sku_code,\n", "                    'product_name': product_name,\n", "                    'category': item_info['category_name'],\n", "                    'score': round(normalized_score, 4),\n", "                    'predicted_quantity': round(qty, 3),\n", "                    'recommendation_type': 'discovery'\n", "                })\n", "        \n", "        # Combine and return\n", "        all_recs = repurchase_recs + discovery_recs\n", "        all_recs.sort(key=lambda x: x['score'], reverse=True)\n", "        return all_recs[:top_n]\n", "    \n", "    def get_recommendations_high_recall_ensemble(self, user_id, user_profile, similarity_dict, top_n=75, repurchase_ratio=None):\n", "        \"\"\"\n", "        🚀 RADICAL HIGH-RECALL ENSEMBLE RECOMMENDATION SYSTEM\n", "        \n", "        Multi-strategy approach designed to achieve 80%+ recall:\n", "        1. Comprehensive Repurchase (ALL items user bought)\n", "        2. Aggressive Collaborative Filtering (relaxed thresholds)  \n", "        3. Category Completion Strategy\n", "        4. Popular Items Fallback\n", "        5. Hierarchical Product Matching\n", "        \"\"\"\n", "        \n", "        if repurchase_ratio is None:\n", "            repurchase_ratio = self.get_user_repurchase_ratio(user_id)\n", "        \n", "        purchased_items = set(user_profile['purchased_items'])  # Now contains configurable product names\n", "        all_recommendations = {}  # Use dict to avoid duplicates\n", "        \n", "        # 🎯 STRATEGY 1: COMPREHENSIVE REPURCHASE (60% of recommendations - MORE AGGRESSIVE)\n", "        repurchase_target = max(10, int(top_n * 0.6))  # Increased from 50% to 60%\n", "        \n", "        # Recommend ALL purchased items (not just top ones)\n", "        repurchase_scores = {}\n", "        for product_name in purchased_items:\n", "            count = user_profile['purchase_counts'].get(product_name, 0)\n", "            # Boost recent purchases and frequent purchases\n", "            repurchase_scores[product_name] = count + 0.5  # Base boost\n", "        \n", "        sorted_repurchase = sorted(repurchase_scores.items(), key=lambda x: x[1], reverse=True)\n", "        \n", "        for i, (product_name, raw_score) in enumerate(sorted_repurchase[:repurchase_target]):\n", "            if product_name in all_recommendations:\n", "                continue\n", "                \n", "            # Higher scores for earlier items\n", "            normalized_score = 0.9 - (i * 0.02)  # Start high, decrease slowly\n", "            normalized_score = max(0.5, normalized_score)  # Minimum 0.5\n", "            \n", "            qty = user_profile['quantities'].get(product_name, self.item_avg_quantities.get(product_name, 1.0))\n", "            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})\n", "            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'\n", "            \n", "            all_recommendations[product_name] = {\n", "                'sku_code': sku_code,\n", "                'product_name': product_name,\n", "                'category': item_info['category_name'],\n", "                'score': round(normalized_score, 4),\n", "                'predicted_quantity': round(qty, 3),\n", "                'recommendation_type': 'repurchase',\n", "                'strategy': 'comprehensive_repurchase'\n", "            }\n", "        \n", "        # 🎯 STRATEGY 2: AGGRESSIVE COLLABORATIVE FILTERING (25% of recommendations)\n", "        collab_target = max(5, int(top_n * 0.25))\n", "        \n", "        discovery_scores = defaultdict(float)\n", "        \n", "        # MUCH more aggressive similarity matching\n", "        for product_name in purchased_items:\n", "            if product_name in similarity_dict:\n", "                count = user_profile['purchase_counts'].get(product_name, 0)\n", "                # Include ALL similar items, not just top ones\n", "                for similar_item, sim in similarity_dict[product_name].items():\n", "                    if similar_item not in purchased_items and similar_item not in all_recommendations:\n", "                        # Lower threshold and higher boost\n", "                        discovery_scores[similar_item] += sim * count * 2  # Double the boost\n", "        \n", "        sorted_collab = sorted(discovery_scores.items(), key=lambda x: x[1], reverse=True)\n", "        \n", "        for i, (product_name, raw_score) in enumerate(sorted_collab[:collab_target]):\n", "            if product_name in all_recommendations:\n", "                continue\n", "                \n", "            normalized_score = 0.8 - (i * 0.03)  # High but decreasing\n", "            normalized_score = max(0.4, normalized_score)\n", "            \n", "            qty = self.item_avg_quantities.get(product_name, 1.0)\n", "            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})\n", "            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'\n", "            \n", "            all_recommendations[product_name] = {\n", "                'sku_code': sku_code,\n", "                'product_name': product_name,\n", "                'category': item_info['category_name'],\n", "                'score': round(normalized_score, 4),\n", "                'predicted_quantity': round(qty, 3),\n", "                'recommendation_type': 'discovery',\n", "                'strategy': 'aggressive_collaborative'\n", "            }\n", "        \n", "        # 🎯 STRATEGY 3: CATEGORY COMPLETION (15% of recommendations)\n", "        category_target = max(3, int(top_n * 0.15))\n", "        \n", "        # For each category user shops in, recommend top items they haven't tried\n", "        category_scores = defaultdict(float)\n", "        for category, preference in user_profile['category_preferences'].items():\n", "            if preference > 0.1:  # User shows interest in this category\n", "                # Find popular items in this category they haven't bought\n", "                for product_name in self.popular_items:\n", "                    if (product_name not in purchased_items and \n", "                        product_name not in all_recommendations and\n", "                        product_name in self.item_categories and\n", "                        self.item_categories[product_name] == category):\n", "                        \n", "                        popularity = self.item_purchase_frequency.get(product_name, 0)\n", "                        category_scores[product_name] += preference * popularity * 150  # Higher boost\n", "        \n", "        sorted_category = sorted(category_scores.items(), key=lambda x: x[1], reverse=True)\n", "        \n", "        for i, (product_name, raw_score) in enumerate(sorted_category[:category_target]):\n", "            if product_name in all_recommendations:\n", "                continue\n", "                \n", "            normalized_score = 0.7 - (i * 0.04)\n", "            normalized_score = max(0.3, normalized_score)\n", "            \n", "            qty = self.item_avg_quantities.get(product_name, 1.0)\n", "            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})\n", "            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'\n", "            \n", "            all_recommendations[product_name] = {\n", "                'sku_code': sku_code,\n", "                'product_name': product_name,\n", "                'category': item_info['category_name'],\n", "                'score': round(normalized_score, 4),\n", "                'predicted_quantity': round(qty, 3),\n", "                'recommendation_type': 'discovery',\n", "                'strategy': 'category_completion'\n", "            }\n", "        \n", "        # 🎯 STRATEGY 4: POPULAR ITEMS FALLBACK (Remaining slots)\n", "        remaining_slots = top_n - len(all_recommendations)\n", "        if remaining_slots > 0:\n", "            # Add globally popular items as safety net\n", "            popular_scores = {}\n", "            for product_name in self.popular_items[:150]:  # Increased from 100 to 150\n", "                if (product_name not in purchased_items and \n", "                    product_name not in all_recommendations):\n", "                    popularity = self.item_purchase_frequency.get(product_name, 0)\n", "                    popular_scores[product_name] = popularity\n", "            \n", "            sorted_popular = sorted(popular_scores.items(), key=lambda x: x[1], reverse=True)\n", "            \n", "            for i, (product_name, raw_score) in enumerate(sorted_popular[:remaining_slots]):\n", "                if product_name in all_recommendations:\n", "                    continue\n", "                    \n", "                normalized_score = 0.5 - (i * 0.02)  # More generous scoring\n", "                normalized_score = max(0.15, normalized_score)  # Lower minimum\n", "                \n", "                qty = self.item_avg_quantities.get(product_name, 1.0)\n", "                item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})\n", "                sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'\n", "                \n", "                all_recommendations[product_name] = {\n", "                    'sku_code': sku_code,\n", "                    'product_name': product_name,\n", "                    'category': item_info['category_name'],\n", "                    'score': round(normalized_score, 4),\n", "                    'predicted_quantity': round(qty, 3),\n", "                    'recommendation_type': 'discovery',\n", "                    'strategy': 'popular_fallback'\n", "                }\n", "        \n", "        # Convert to list and sort by score\n", "        final_recommendations = list(all_recommendations.values())\n", "        final_recommendations.sort(key=lambda x: x['score'], reverse=True)\n", "        \n", "        return final_recommendations[:top_n]\n", "\n", "    def get_recommendations_with_intelligent_deduplication(self, user_id, user_profile, similarity_dict, current_date, top_n=75, repurchase_ratio=None, enable_deduplication=None):\n", "        \"\"\"\n", "        🧠 INTELLIGENT DEDUPLICATION-ENHANCED RECOMMENDATION SYSTEM\n", "\n", "        Enhanced version of the high-recall ensemble system with intelligent deduplication:\n", "        1. Generate initial recommendations using ensemble approach\n", "        2. Analyze daily purchase patterns for the user\n", "        3. Apply intelligent deduplication logic\n", "        4. Replace blocked items with smart alternatives\n", "        5. Ensure recommendation diversity while preserving daily essentials\n", "\n", "        Args:\n", "            user_id: User ID\n", "            user_profile: User's purchase profile\n", "            similarity_dict: Item similarity matrix\n", "            current_date: Current evaluation date for deduplication analysis\n", "            top_n: Number of recommendations to generate\n", "            repurchase_ratio: User's repurchase ratio\n", "            enable_deduplication: Override for deduplication setting (None uses default)\n", "\n", "        Returns:\n", "            list: Intelligently deduplicated recommendations\n", "        \"\"\"\n", "\n", "        # Override deduplication setting if specified\n", "        original_setting = self.deduplication_enabled\n", "        if enable_deduplication is not None:\n", "            self.deduplication_enabled = enable_deduplication\n", "\n", "        try:\n", "            # Step 1: Generate initial recommendations using existing ensemble approach\n", "            initial_recommendations = self.get_recommendations_high_recall_ensemble(\n", "                user_id, user_profile, similarity_dict, int(top_n * 1.5), repurchase_ratio  # Generate 50% more to allow for filtering\n", "            )\n", "\n", "            # Step 2: Apply intelligent deduplication\n", "            deduplicated_recommendations = self.apply_intelligent_deduplication(\n", "                initial_recommendations, user_id, current_date, user_profile, similarity_dict\n", "            )\n", "        finally:\n", "            # Restore original setting\n", "            self.deduplication_enabled = original_setting\n", "\n", "        # Step 3: If we don't have enough recommendations after deduplication, generate more alternatives\n", "        if len(deduplicated_recommendations) < top_n:\n", "            needed = top_n - len(deduplicated_recommendations)\n", "            existing_items = set(rec['product_name'] for rec in deduplicated_recommendations)\n", "\n", "            # Generate additional discovery recommendations\n", "            additional_recs = []\n", "            for category, preference in user_profile.get('category_preferences', {}).items():\n", "                if preference > 0.05 and len(additional_recs) < needed:\n", "                    category_items = [\n", "                        item for item in self.popular_items\n", "                        if self.item_categories.get(item) == category\n", "                        and item not in existing_items\n", "                        and item not in user_profile.get('purchased_items', [])\n", "                        and not self.is_item_in_cooldown(user_id, item, current_date)\n", "                    ]\n", "\n", "                    for item in category_items[:2]:  # Add up to 2 items per category\n", "                        if len(additional_recs) < needed:\n", "                            item_info = self.catalogue_lookup.get(item, {'sku_codes': ['UNKNOWN'], 'category_name': 'Unknown'})\n", "                            additional_rec = {\n", "                                'sku_code': item_info['sku_codes'][0],\n", "                                'product_name': item,\n", "                                'category': item_info.get('category_name', 'Unknown'),\n", "                                'score': 0.4,  # Lower score for additional items\n", "                                'predicted_quantity': self.item_avg_quantities.get(item, 1.0),\n", "                                'recommendation_type': 'discovery',\n", "                                'strategy': 'deduplication_filler'\n", "                            }\n", "                            additional_recs.append(additional_rec)\n", "                            # Update tracking\n", "                            self.update_recommendation_tracking(user_id, item, current_date)\n", "\n", "            deduplicated_recommendations.extend(additional_recs)\n", "\n", "        # Step 4: Sort by score and return top_n\n", "        deduplicated_recommendations.sort(key=lambda x: x['score'], reverse=True)\n", "        return deduplicated_recommendations[:top_n]\n", "    \n", "    def calculate_personalized_k_values(self, customer_orders_df, lookback_purchases=15):\n", "        \"\"\"Calculate personalized k values based on median items in user's last N purchases\"\"\"\n", "        \n", "        print(f\"🎯 Calculating personalized k values based on median items in last {lookback_purchases} purchases...\")\n", "        \n", "        # Convert to datetime if not already\n", "        if not pd.api.types.is_datetime64_any_dtype(customer_orders_df['delivery_date']):\n", "            customer_orders_df = customer_orders_df.copy()\n", "            customer_orders_df['delivery_date'] = pd.to_datetime(customer_orders_df['delivery_date'])\n", "        \n", "        personalized_k = {}\n", "        \n", "        # Group by customer and calculate median items per purchase\n", "        for customer_id, customer_orders in customer_orders_df.groupby('customer_id'):\n", "            # Sort by delivery date descending to get most recent purchases first\n", "            customer_orders_sorted = customer_orders.sort_values('delivery_date', ascending=False)\n", "            \n", "            # Get unique orders (display_order_id) and their item counts\n", "            order_item_counts = customer_orders_sorted.groupby('display_order_id').size()\n", "            \n", "            # Take the last N purchases\n", "            recent_purchases = order_item_counts.head(lookback_purchases)\n", "            \n", "            if len(recent_purchases) > 0:\n", "                # Calculate median number of items per purchase\n", "                median_items = recent_purchases.median()\n", "                \n", "                # Set minimum k=3 and maximum k=50 for practical reasons\n", "                personalized_k[customer_id] = max(3, min(50, int(median_items)))\n", "            else:\n", "                # Default fallback if no purchase history\n", "                personalized_k[customer_id] = 10\n", "        \n", "        # Summary statistics\n", "        k_values = list(personalized_k.values())\n", "        print(f\"✅ Calculated personalized k for {len(personalized_k):,} users\")\n", "        print(f\"   Median k across all users: {np.median(k_values):.1f}\")\n", "        print(f\"   Mean k across all users: {np.mean(k_values):.1f}\")\n", "        print(f\"   Min k: {min(k_values)}, Max k: {max(k_values)}\")\n", "        \n", "        # Distribution of k values\n", "        k_distribution = pd.Series(k_values).value_counts().sort_index()\n", "        print(f\"   Most common k values: {k_distribution.head(5).to_dict()}\")\n", "        \n", "        return personalized_k\n", "\n", "    def evaluate_precision_recall_ultra_fast(self, customer_orders_df, k=None, test_days=7, use_personalized_k=True):\n", "        \"\"\"🚀 HIGH-RECALL EVALUATION using Multi-Strategy Ensemble with PERSONALIZED k values\"\"\"\n", "        \n", "        if use_personalized_k:\n", "            print(f\"🚀 HIGH-RECALL PRECISION & RECALL EVALUATION WITH PERSONALIZED k VALUES\")\n", "            print(f\"🎯 Using median items from last 15 purchases as personalized k for each user\")\n", "        else:\n", "            print(f\"🚀 HIGH-RECALL PRECISION & RECALL @ k={k} EVALUATION (USING {self.product_name_column.upper()})\")\n", "        print(f\"🎯 TARGET: 80%+ RECALL with Multi-Strategy Ensemble Approach using '{self.product_name_column}' column\")\n", "        print(\"=\"*70)\n", "        \n", "        start_total = time.time()\n", "        \n", "        # Convert orders to use product names\n", "        orders_with_names = customer_orders_df.copy()\n", "        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)\n", "        orders_with_names = orders_with_names.dropna(subset=['product_name'])\n", "        \n", "        # Calculate personalized k values if requested\n", "        if use_personalized_k:\n", "            personalized_k_values = self.calculate_personalized_k_values(customer_orders_df)\n", "            default_k = int(np.median(list(personalized_k_values.values())))\n", "            print(f\"📊 Using personalized k values (median: {default_k})\")\n", "        else:\n", "            personalized_k_values = {}\n", "            default_k = k if k is not None else 10\n", "            print(f\"📊 Using fixed k={default_k} for all users\")\n", "        \n", "        # Get evaluation dates\n", "        max_date = orders_with_names['delivery_date'].max()\n", "        evaluation_dates = [max_date - timedelta(days=i) for i in range(test_days-1, -1, -1)]\n", "        today_date = pd.Timestamp(datetime.today().date())\n", "        if today_date not in evaluation_dates:\n", "            evaluation_dates.append(datetime.today().date())\n", "        \n", "        all_user_metrics = []\n", "        daily_results = {}\n", "        all_recommendations = []  # Store all recommendations for the new dataframe\n", "        \n", "        # Pre-compute test data for ALL dates at once (batch operation) - using configurable product names\n", "        print(f\"📊 Pre-computing test data for all dates using {self.product_name_column}...\")\n", "        test_data_all = {}\n", "        for eval_date in evaluation_dates:\n", "            test_data = orders_with_names[orders_with_names['delivery_date'] == eval_date]\n", "            if len(test_data) > 0:\n", "                # Group by customer and create sets of purchased product names\n", "                test_grouped = test_data.groupby('customer_id')['product_name'].apply(set).to_dict()\n", "                test_data_all[eval_date] = test_grouped\n", "        \n", "        for eval_date in evaluation_dates:\n", "            if eval_date not in test_data_all:\n", "                continue\n", "                \n", "            print(f\"\\n📅 {eval_date.strftime('%Y-%m-%d')}\")\n", "            day_start = time.time()\n", "            \n", "            # Training data (using original orders but will convert to names in build_matrices)\n", "            train_data = customer_orders_df[customer_orders_df['delivery_date'] < eval_date]\n", "            \n", "            if len(train_data) == 0:\n", "                continue\n", "            \n", "            print(f\"   📊 Train: {len(train_data)} orders | Test: {len(test_data_all[eval_date])} users\")\n", "            \n", "            # Build model (with caching) - now uses configurable product names internally\n", "            model_data = self.build_matrices_ultra_fast(train_data, eval_date)\n", "            user_profiles = self.build_user_profiles_lightning_fast(\n", "                model_data['user_item_matrix'], \n", "                model_data['user_item_qty_matrix']\n", "            )\n", "            \n", "            # 🧠 ANALYZE DAILY PURCHASE PATTERNS for intelligent deduplication\n", "            self.analyze_daily_purchase_patterns(customer_orders_df, eval_date)\n", "\n", "            # 🔍 DEBUG: Show tracking status for debug users\n", "            debug_users = ['570fa519', '4bcb4707']\n", "            for debug_user_prefix in debug_users:\n", "                debug_user = next((uid for uid in user_profiles.keys() if uid.startswith(debug_user_prefix)), None)\n", "                if debug_user and debug_user in self.user_last_recommended:\n", "                    tracked_items = self.user_last_recommended[debug_user]\n", "                    print(f\"🔍 DEBUG: User {debug_user[:8]}... tracking: {len(tracked_items)} items\")\n", "                    for item, date in list(tracked_items.items())[:3]:\n", "                        days_ago = (eval_date - date).days\n", "                        print(f\"      {item}: {days_ago} days ago\")\n", "\n", "            # Generate recommendations using INTELLIGENT DEDUPLICATION SYSTEM with personalized k values\n", "            print(f\"   🧠 Generating INTELLIGENT DEDUPLICATED recommendations using Multi-Strategy Ensemble with {self.product_name_column}...\")\n", "            recommendations = {}\n", "            user_k_values = {}  # Track k values used for each user\n", "\n", "            for user_id in user_profiles:\n", "                # Use personalized k value if available, otherwise use default\n", "                user_k = personalized_k_values.get(user_id, default_k)\n", "                user_k_values[user_id] = user_k\n", "\n", "                recommendations[user_id] = self.get_recommendations_with_intelligent_deduplication(\n", "                    user_id, user_profiles[user_id], model_data['similarity_dict'], eval_date, user_k\n", "                )\n", "            \n", "            # Collect all recommendations for the output dataframe with personalized filtering\n", "            for user_id, user_recs in recommendations.items():\n", "                user_repurchase_ratio = self.get_user_repurchase_ratio(user_id)\n", "                user_k = user_k_values.get(user_id, default_k)\n", "                \n", "                # 🚀 EXTREMELY PERMISSIVE thresholds for 80%+ RECALL (final push!)\n", "                if user_repurchase_ratio >= 0.8:  # Conservative user\n", "                    personalized_threshold = 0.15  # FURTHER LOWERED from 0.25 to 0.15\n", "                    user_type = 'Conservative'\n", "                elif user_repurchase_ratio >= 0.5:  # Moderate user\n", "                    personalized_threshold = 0.10  # FURTHER LOWERED from 0.15 to 0.10\n", "                    user_type = 'Moderate'\n", "                else:  # Exploratory user\n", "                    personalized_threshold = 0.05  # FURTHER LOWERED from 0.10 to 0.05\n", "                    user_type = 'Exploratory'\n", "                \n", "                for rec in user_recs:\n", "                    # Calculate purchase probability based on recommendation context\n", "                    purchase_probability = self.calculate_purchase_probability(\n", "                        rec, user_repurchase_ratio, user_id\n", "                    )\n", "                    \n", "                    # Only include recommendations that meet the personalized threshold\n", "                    if purchase_probability >= personalized_threshold:\n", "                        recommendation_record = {\n", "                            'sku_name': rec['product_name'],  # Keep backward compatibility\n", "                            'product_name': rec['product_name'],  # New column for clarity\n", "                            'sku_code': rec['sku_code'],\n", "                            'show_recommendation': True,  # Set to True for filtered recommendations\n", "                            'recommendation_type': rec['recommendation_type'],\n", "                            'recommendation_score': rec['score'],\n", "                            'purchase_probability': purchase_probability,  # New column with purchase confidence\n", "                            'recommendation_date': eval_date,\n", "                            'predicted_quantity': rec['predicted_quantity'],\n", "                            'customer_id': user_id,\n", "                            'category': rec['category'],\n", "                            'user_repurchase_ratio': user_repurchase_ratio,  # Track the ratio used\n", "                            'personalized_threshold': personalized_threshold,  # Track the threshold used\n", "                            'user_type': user_type,  # Track user classification\n", "                            'product_name_column_used': self.product_name_column,  # Track which column was used\n", "                            'personalized_k': user_k  # Track the k value used for this user\n", "                        }\n", "                        all_recommendations.append(recommendation_record)\n", "            \n", "            # Evaluate (vectorized) - now using configurable product names for comparison\n", "            print(f\"   📊 Computing metrics using {self.product_name_column}...\")\n", "            actual_purchases = test_data_all[eval_date]\n", "            \n", "            metrics_list = []\n", "            for user_id in recommendations:\n", "                if user_id in actual_purchases:\n", "                    # Get recommended product names (not SKU codes)\n", "                    recommended_items = set([rec['product_name'] for rec in recommendations[user_id]])\n", "                    actual_items = actual_purchases[user_id]  # Already contains configurable product names\n", "                    user_k = user_k_values.get(user_id, default_k)\n", "                    \n", "                    hits = len(recommended_items.intersection(actual_items))\n", "                    precision = hits / len(recommended_items) if len(recommended_items) > 0 else 0\n", "                    recall = hits / len(actual_items) if len(actual_items) > 0 else 0\n", "                    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n", "                    hit_rate = 1 if hits > 0 else 0\n", "                    \n", "                    # Get user repurchase ratio for bucketing\n", "                    user_repurchase_ratio = self.get_user_repurchase_ratio(user_id)\n", "                    \n", "                    # Categorize user based on repurchase ratio\n", "                    if user_repurchase_ratio >= 0.8:\n", "                        user_bucket = 'repurchase'  # High repurchase users\n", "                    elif user_repurchase_ratio >= 0.5:\n", "                        user_bucket = 'balanced'    # Moderate repurchase users\n", "                    else:\n", "                        user_bucket = 'discovery'   # Low repurchase users (more exploratory)\n", "                    \n", "                    metrics_list.append({\n", "                        'customer_id': user_id,  # Changed from user_id to customer_id for consistency\n", "                        'user_id': user_id,      # Keep both for backward compatibility\n", "                        'precision_at_k': precision,\n", "                        'recall_at_k': recall,\n", "                        'f1_at_k': f1,\n", "                        'hit_rate': hit_rate,\n", "                        'hits': hits,\n", "                        'total_recommended': len(recommended_items),\n", "                        'total_actual': len(actual_items),\n", "                        'personalized_k': user_k,\n", "                        'user_repurchase_ratio': user_repurchase_ratio,\n", "                        'user_bucket': user_bucket\n", "                    })\n", "            \n", "            # Daily averages (vectorized)\n", "            if metrics_list:\n", "                k_values_today = [m['personalized_k'] for m in metrics_list]\n", "                daily_avg = {\n", "                    'date': eval_date,\n", "                    'users_evaluated': len(metrics_list),\n", "                    'avg_precision_at_k': np.mean([m['precision_at_k'] for m in metrics_list]),\n", "                    'avg_recall_at_k': np.mean([m['recall_at_k'] for m in metrics_list]),\n", "                    'avg_f1_at_k': np.mean([m['f1_at_k'] for m in metrics_list]),\n", "                    'avg_hit_rate': np.mean([m['hit_rate'] for m in metrics_list]),\n", "                    'median_k': np.median(k_values_today),\n", "                    'mean_k': np.mean(k_values_today),\n", "                    'min_k': np.min(k_values_today),\n", "                    'max_k': np.max(k_values_today),\n", "                    'use_personalized_k': use_personalized_k\n", "                }\n", "                daily_results[eval_date] = daily_avg\n", "                \n", "                # Add to overall metrics\n", "                for m in metrics_list:\n", "                    m.update({'date': eval_date, 'use_personalized_k': use_personalized_k})\n", "                    all_user_metrics.append(m)\n", "                \n", "                print(f\"   👥 Users: {len(metrics_list)} | P@k: {daily_avg['avg_precision_at_k']:.3f} | R@k: {daily_avg['avg_recall_at_k']:.3f} | Med k: {daily_avg['median_k']:.0f}\")\n", "            \n", "            day_time = time.time() - day_start\n", "            print(f\"   ⏱️ Day completed in {day_time:.1f}s\")\n", "        \n", "        # Final results\n", "        if all_user_metrics:\n", "            all_k_values = [m['personalized_k'] for m in all_user_metrics]\n", "            overall_metrics = {\n", "                'use_personalized_k': use_personalized_k,\n", "                'median_k': np.median(all_k_values),\n", "                'mean_k': np.mean(all_k_values),\n", "                'min_k': np.min(all_k_values),\n", "                'max_k': np.max(all_k_values),\n", "                'test_days': test_days,\n", "                'total_evaluations': len(all_user_metrics),\n", "                'total_days_evaluated': len(daily_results),\n", "                'overall_avg_precision_at_k': np.mean([m['precision_at_k'] for m in all_user_metrics]),\n", "                'overall_avg_recall_at_k': np.mean([m['recall_at_k'] for m in all_user_metrics]),\n", "                'overall_avg_f1_at_k': np.mean([m['f1_at_k'] for m in all_user_metrics]),\n", "                'overall_avg_hit_rate': np.mean([m['hit_rate'] for m in all_user_metrics]),\n", "                'product_name_column_used': self.product_name_column\n", "            }\n", "        else:\n", "            overall_metrics = {}\n", "        \n", "        total_time = time.time() - start_total\n", "        \n", "        print(\"\\n\" + \"=\"*70)\n", "        print(f\"🏆 ULTRA-FAST EVALUATION COMPLETED IN {total_time:.1f} SECONDS!\")\n", "        print(f\"🔍 NOW USING {self.product_name_column.upper()} FOR RECOMMENDATION MATCHING!\")\n", "        print(\"=\"*70)\n", "        if overall_metrics:\n", "            print(f\"📊 Days evaluated: {overall_metrics['total_days_evaluated']}\")\n", "            print(f\"📊 User evaluations: {overall_metrics['total_evaluations']:,}\")\n", "            print(f\"📊 Product name column used: {overall_metrics['product_name_column_used']}\")\n", "            \n", "            if use_personalized_k:\n", "                print(f\"📊 Personalized k values used: median={overall_metrics['median_k']:.0f}, mean={overall_metrics['mean_k']:.1f}, range={overall_metrics['min_k']:.0f}-{overall_metrics['max_k']:.0f}\")\n", "                print(f\"\\n🎯 FINAL HIGH-RECALL RESULTS @ PERSONALIZED k ({self.product_name_column.upper()} MATCHING):\")\n", "            else:\n", "                print(f\"\\n🎯 FINAL HIGH-RECALL RESULTS @ k={default_k} ({self.product_name_column.upper()} MATCHING):\")\n", "            \n", "            print(f\"   Precision@k: {overall_metrics['overall_avg_precision_at_k']:.4f}\")\n", "            print(f\"   Recall@k: {overall_metrics['overall_avg_recall_at_k']:.4f}\")\n", "            print(f\"   F1@k: {overall_metrics['overall_avg_f1_at_k']:.4f}\")\n", "            print(f\"   Hit Rate: {overall_metrics['overall_avg_hit_rate']:.4f}\")\n", "            \n", "            # 🎯 RECALL TARGET CHECK\n", "            if overall_metrics['overall_avg_recall_at_k'] >= 0.8:\n", "                print(f\"   🎉 SUCCESS! ACHIEVED 80%+ RECALL TARGET!\")\n", "            else:\n", "                print(f\"   ⚠️  Still below 80% target. Current: {overall_metrics['overall_avg_recall_at_k']*100:.1f}%\")\n", "        print(\"=\"*70)\n", "        \n", "        return {\n", "            'overall_metrics': overall_metrics,\n", "            'daily_results': daily_results,\n", "            'all_user_metrics': all_user_metrics,\n", "            'all_recommendations': all_recommendations,\n", "            'total_time_seconds': total_time\n", "        }\n", "\n", "# def main():\n", "print(\"🚀 ULTRA-OPTIMIZED PRECISION & RECALL EVALUATION WITH PERSONALIZED k VALUES\")\n", "print(\"=\"*70)\n", "print(\"🎯 Maximum speed optimization with intelligent caching\")\n", "print(\"🔧 NEW: Configurable product name column (name vs sku_parent_name)\")\n", "print(\"🎯 NEW: Personalized k values based on user's last 15 purchase patterns\")\n", "print(\"=\"*70)\n", "\n", "# Pre-compute all static features (one-time cost)\n", "print(\"\\n🔧 SETUP PHASE:\")\n", "setup_start = time.time()\n", "\n", "# Convert delivery_date to datetime\n", "customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])\n", "\n", "# Configure which product name column to use\n", "# Options: 'name' or 'sku_parent_name'\n", "# Based on previous testing: 'sku_parent_name' generally gives higher recall\n", "# PRODUCT_NAME_COLUMN = 'sku_parent_name'  # Change this to 'name' if needed\n", "PRODUCT_NAME_COLUMN = 'name'\n", "\n", "print(f\"📊 Available columns in catalogue: {list(catalogue_rc.columns)}\")\n", "print(f\"🔧 Using '{PRODUCT_NAME_COLUMN}' column for product identification\")\n", "\n", "# Validate that both columns exist (assuming they are always present)\n", "if 'name' not in catalogue_rc.columns or 'sku_parent_name' not in catalogue_rc.columns:\n", "    raise ValueError(\"Both 'name' and 'sku_parent_name' columns must be present in the catalogue\")\n", "\n", "# Show comparison stats\n", "unique_names = catalogue_rc['name'].nunique()\n", "unique_parent_names = catalogue_rc['sku_parent_name'].nunique()\n", "print(f\"📊 Unique 'name' values: {unique_names:,}\")\n", "print(f\"📊 Unique 'sku_parent_name' values: {unique_parent_names:,}\")\n", "\n", "# Initialize system with chosen column\n", "system = UltraOptimizedRecommendationSystem(\n", "    user_repurchase_ratios_df=repurchase_ratios_df,\n", "    product_name_column=PRODUCT_NAME_COLUMN\n", ")\n", "\n", "system.precompute_static_features(customer_orders_rc, catalogue_rc)\n", "\n", "# Run evaluation with chosen column and personalized k values\n", "results = system.evaluate_precision_recall_ultra_fast(customer_orders_rc, test_days=7, use_personalized_k=True)\n", "\n", "setup_time = time.time() - setup_start\n", "print(f\"\\n✅ Complete setup and evaluation completed in {setup_time:.2f} seconds\")\n", "\n", "# Save results\n", "if results['overall_metrics']:\n", "    print(\"\\n💾 Saving results...\")\n", "    \n", "    # Convert to DataFrames\n", "    user_metrics_df = pd.DataFrame(results['all_user_metrics'])\n", "    daily_metrics_df = pd.DataFrame([\n", "        {**metrics, 'date': date.strftime('%Y-%m-%d')} \n", "        for date, metrics in results['daily_results'].items()\n", "    ])\n", "    recommendations_df = pd.DataFrame(results['all_recommendations'])\n", "    \n", "    # Analyze performance by user bucket\n", "    if len(user_metrics_df) > 0 and 'user_bucket' in user_metrics_df.columns:\n", "        print(\"\\n📊 RECALL PERFORMANCE BY USER BUCKET:\")\n", "        print(\"=\"*60)\n", "        \n", "        bucket_analysis = user_metrics_df.groupby('user_bucket').agg({\n", "            'recall_at_k': ['mean', 'median', 'std', 'count'],\n", "            'precision_at_k': ['mean', 'median'],\n", "            'f1_at_k': ['mean', 'median'],\n", "            'hit_rate': 'mean',\n", "            'personalized_k': 'mean',\n", "            'user_repurchase_ratio': 'mean'\n", "        }).round(4)\n", "        \n", "        # Flatten column names\n", "        bucket_analysis.columns = ['_'.join(col).strip() for col in bucket_analysis.columns]\n", "        \n", "        print(\"User Bucket Performance Summary:\")\n", "        print(\"-\" * 60)\n", "        \n", "        for bucket in ['repurchase', 'balanced', 'discovery']:\n", "            if bucket in bucket_analysis.index:\n", "                row = bucket_analysis.loc[bucket]\n", "                print(f\"\\n{bucket.upper()} Users (repurchase ratio ≥ {0.8 if bucket=='repurchase' else 0.5 if bucket=='balanced' else 0.0}):\")\n", "                print(f\"  Count: {int(row['recall_at_k_count'])} users\")\n", "                print(f\"  Mean Recall@k: {row['recall_at_k_mean']:.4f} (±{row['recall_at_k_std']:.4f})\")\n", "                print(f\"  Median Recall@k: {row['recall_at_k_median']:.4f}\")\n", "                print(f\"  Mean Precision@k: {row['precision_at_k_mean']:.4f}\")\n", "                print(f\"  Hit Rate: {row['hit_rate_mean']:.4f}\")\n", "                print(f\"  Avg k value: {row['personalized_k_mean']:.1f}\")\n", "                print(f\"  Avg repurchase ratio: {row['user_repurchase_ratio_mean']:.3f}\")\n", "        \n", "        # Show best and worst performing bucket\n", "        bucket_recall_means = bucket_analysis['recall_at_k_mean']\n", "        if len(bucket_recall_means) > 1:\n", "            best_bucket = bucket_recall_means.idxmax()\n", "            worst_bucket = bucket_recall_means.idxmin()\n", "            print(f\"\\n🏆 Best performing bucket: {best_bucket.upper()} (recall: {bucket_recall_means[best_bucket]:.4f})\")\n", "            print(f\"⚠️  Worst performing bucket: {worst_bucket.upper()} (recall: {bucket_recall_means[worst_bucket]:.4f})\")\n", "        \n", "        print(\"=\"*60)\n", "    \n", "    # Print filtering summary\n", "    print(\"\\n📊 PERSONALIZED FILTERING SUMMARY:\")\n", "    print(\"=\"*60)\n", "    if len(recommendations_df) > 0:\n", "        # Show overall statistics\n", "        total_users = recommendations_df['customer_id'].nunique()\n", "        total_recommendations = len(recommendations_df)\n", "        product_column_used = recommendations_df['product_name_column_used'].iloc[0]\n", "        \n", "        print(f\"Product name column used: {product_column_used}\")\n", "        print(f\"Total users: {total_users:,}\")\n", "        print(f\"Total personalized recommendations: {total_recommendations:,}\")\n", "        print(f\"Average recommendations per user: {total_recommendations/total_users:.1f}\")\n", "        \n", "        # Show k value distribution\n", "        if 'personalized_k' in recommendations_df.columns:\n", "            k_stats = recommendations_df['personalized_k'].describe()\n", "            print(f\"\\nPersonalized k value distribution:\")\n", "            print(f\"  Median k: {k_stats['50%']:.0f}\")\n", "            print(f\"  Mean k: {k_stats['mean']:.1f}\")\n", "            print(f\"  Range: {k_stats['min']:.0f} - {k_stats['max']:.0f}\")\n", "            \n", "            # Show most common k values\n", "            k_value_counts = recommendations_df['personalized_k'].value_counts().head(5)\n", "            print(f\"  Most common k values: {dict(k_value_counts)}\")\n", "        \n", "        # Show breakdown by user type\n", "        user_type_breakdown = recommendations_df['user_type'].value_counts()\n", "        print(f\"\\nUser type breakdown:\")\n", "        for user_type, count in user_type_breakdown.items():\n", "            threshold = recommendations_df[recommendations_df['user_type'] == user_type]['personalized_threshold'].iloc[0]\n", "            print(f\"  {user_type}: {count:,} recommendations (threshold ≥ {threshold})\")\n", "        \n", "        # Show purchase probability distribution\n", "        print(f\"\\nPurchase probability distribution:\")\n", "        print(f\"  High confidence (≥0.6): {len(recommendations_df[recommendations_df['purchase_probability'] >= 0.6]):,}\")\n", "        print(f\"  Medium confidence (0.4-0.59): {len(recommendations_df[(recommendations_df['purchase_probability'] >= 0.4) & (recommendations_df['purchase_probability'] < 0.6)]):,}\")\n", "        print(f\"  Low confidence (0.3-0.39): {len(recommendations_df[(recommendations_df['purchase_probability'] >= 0.3) & (recommendations_df['purchase_probability'] < 0.4)]):,}\")\n", "        \n", "        # Show sample recommendations for each user type\n", "        print(f\"\\n📋 Sample recommendations by user type:\")\n", "        for user_type in ['Conservative', 'Moderate', 'Exploratory']:\n", "            if user_type in user_type_breakdown.index:\n", "                sample_user_recs = recommendations_df[recommendations_df['user_type'] == user_type].head(3)\n", "                print(f\"\\n  {user_type} user examples:\")\n", "                for _, rec in sample_user_recs.iterrows():\n", "                    print(f\"    • {rec['product_name']} (Prob: {rec['purchase_probability']:.3f}, Type: {rec['recommendation_type']})\")\n", "    \n", "    # Plot 1: Recall Distribution\n", "    if len(user_metrics_df) > 0 and 'recall_at_k' in user_metrics_df.columns:\n", "        # Create plotly histogram for recall distribution\n", "        fig_1 = go.Figure()\n", "        \n", "        # Add histogram\n", "        fig_1.add_trace(go.Histogram(\n", "            x=user_metrics_df['recall_at_k'],\n", "            nbinsx=30,\n", "            opacity=0.7,\n", "            marker_color='skyblue',\n", "            marker_line_color='black',\n", "            marker_line_width=1,\n", "            name='Recall@10'\n", "        ))\n", "    \n", "        # Calculate mean and median values\n", "        mean_recall = user_metrics_df['recall_at_k'].mean()\n", "        median_recall = user_metrics_df['recall_at_k'].median()\n", "    \n", "        # Add vertical line for mean with adjusted annotation position\n", "        fig_1.add_vline(\n", "            x=mean_recall,\n", "            line_dash=\"dash\",\n", "            line_color=\"red\",\n", "            annotation_text=f\"Mean: {mean_recall:.3f}\",\n", "            annotation_position=\"top left\",\n", "            annotation_font=dict(size=10, color=\"red\")\n", "        )\n", "    \n", "        # Add vertical line for median with adjusted annotation position\n", "        fig_1.add_vline(\n", "            x=median_recall,\n", "            line_dash=\"dash\",\n", "            line_color=\"orange\",\n", "            annotation_text=f\"Median: {median_recall:.3f}\",\n", "            annotation_position=\"top right\",\n", "            annotation_font=dict(size=10, color=\"orange\")\n", "        )\n", "    \n", "        # Update layout with additional margin\n", "        fig_1.update_layout(\n", "            title='Recall@10 Distribution',\n", "            xaxis_title='Recall@10',\n", "            yaxis_title='Frequency',\n", "            showlegend=False,\n", "            template='plotly_white',\n", "            margin=dict(l=40, r=40, t=60, b=40)\n", "        )\n", "    \n", "        print(f\"✅ Recall distribution plot created (n={len(user_metrics_df)} evaluations)\")\n", "    \n", "    else:\n", "        print(\"⚠️ No recall data available for plotting\")\n", "        fig_1 = go.Figure()  # Empty figure as fallback\n", "    \n", "    # Plot 2: Recommendation Score Distribution  \n", "    if len(recommendations_df) > 0 and 'recommendation_score' in recommendations_df.columns:\n", "        # Create plotly histogram for recommendation score distribution\n", "        fig_2 = go.Figure()\n", "        \n", "        # Add histogram\n", "        fig_2.add_trace(go.Histogram(\n", "            x=recommendations_df['recommendation_score'],\n", "            nbinsx=30,\n", "            opacity=0.7,\n", "            marker_color='lightgreen',\n", "            marker_line_color='black',\n", "            marker_line_width=1,\n", "            name='Recommendation Score'\n", "        ))\n", "    \n", "        # Calculate mean and median values\n", "        mean_score = recommendations_df['recommendation_score'].mean()\n", "        median_score = recommendations_df['recommendation_score'].median()\n", "    \n", "        # Add vertical line for mean with adjusted annotation position\n", "        fig_2.add_vline(\n", "            x=mean_score,\n", "            line_dash=\"dash\",\n", "            line_color=\"red\",\n", "            annotation_text=f\"Mean: {mean_score:.3f}\",\n", "            annotation_position=\"top left\",\n", "            annotation_font=dict(size=10, color=\"red\")\n", "        )\n", "    \n", "        # Add vertical line for median with adjusted annotation position\n", "        fig_2.add_vline(\n", "            x=median_score,\n", "            line_dash=\"dash\",\n", "            line_color=\"orange\",\n", "            annotation_text=f\"Median: {median_score:.3f}\",\n", "            annotation_position=\"top right\",\n", "            annotation_font=dict(size=10, color=\"orange\")\n", "        )\n", "    \n", "        # Update layout with additional margin\n", "        fig_2.update_layout(\n", "            title='Recommendation Score Distribution',\n", "            xaxis_title='Recommendation Score',\n", "            yaxis_title='Frequency',\n", "            showlegend=False,\n", "            template='plotly_white',\n", "            margin=dict(l=40, r=40, t=60, b=40)\n", "        )\n", "    \n", "        print(f\"✅ Recommendation score distribution plot created (n={len(recommendations_df)} recommendations)\")\n", "    \n", "    else:\n", "        print(\"⚠️ No recommendation score data available for plotting\")\n", "        fig_2 = go.Figure()  # Empty figure as fallback\n", "\n", "import random\n", "\n", "# Set a fixed random seed for reproducibility\n", "RANDOM_SEED = 42\n", "random.seed(RANDOM_SEED)\n", "np.random.seed(RANDOM_SEED)\n", "\n", "# 1. Get user segments from user_metrics_df\n", "segment_map = {\n", "    'repurchase': 'repurchase',\n", "    'balanced': 'balanced',\n", "    'discovery': 'discovery',\n", "}\n", "\n", "# Ensure user_metrics_df and recommendations_df exist\n", "if 'user_metrics_df' in locals() and 'recommendations_df' in locals():\n", "    # 2. Select 20 random users from each segment (if available)\n", "    selected_users = []\n", "    for segment in segment_map:\n", "        users_in_segment = user_metrics_df[user_metrics_df['user_bucket'] == segment]['customer_id'].unique()\n", "        if len(users_in_segment) > 20:\n", "            sampled_users = random.sample(list(users_in_segment), 20)\n", "        else:\n", "            sampled_users = list(users_in_segment)\n", "        selected_users.extend([(u, segment) for u in sampled_users])\n", "\n", "    # 3. Get max delivery_date in customer_orders_rc\n", "    max_date = pd.to_datetime(customer_orders_rc['delivery_date']).max()\n", "    min_date = max_date - pd.Timedelta(days=6)  # last 7 days (inclusive)\n", "\n", "    # 4. Prepare output rows with frequency counts and filter out empty purchases\n", "    output_rows = []\n", "    \n", "    # Define the start date for frequency calculation (June 1st, 2025)\n", "    frequency_start_date = pd.to_datetime('2025-06-01')\n", "    \n", "    for customer_id, segment in selected_users:\n", "        # For each day in the last week\n", "        for day in pd.date_range(min_date, max_date):\n", "            # Actual purchased items for this specific day\n", "            actual_items = customer_orders_rc[\n", "                (customer_orders_rc['customer_id'] == customer_id) &\n", "                (pd.to_datetime(customer_orders_rc['delivery_date']) == day)\n", "            ]\n", "            \n", "            # Skip this day if no purchases were made\n", "            if len(actual_items) == 0:\n", "                continue\n", "            \n", "            # Get actual items for this day\n", "            if PRODUCT_NAME_COLUMN in actual_items.columns:\n", "                actual_items_set = set(actual_items[PRODUCT_NAME_COLUMN])\n", "            else:\n", "                # fallback to 'product_name' if present\n", "                actual_items_set = set(actual_items['product_name']) if 'product_name' in actual_items.columns else set()\n", "            \n", "            # Calculate frequency counts for items purchased from June 1st, 2025 up to this specific delivery date\n", "            user_purchases_up_to_date = customer_orders_rc[\n", "                (customer_orders_rc['customer_id'] == customer_id) &\n", "                (pd.to_datetime(customer_orders_rc['delivery_date']) >= frequency_start_date) &\n", "                (pd.to_datetime(customer_orders_rc['delivery_date']) <= day)\n", "            ]\n", "            \n", "            # Create frequency dictionary\n", "            actual_items_frequency = {}\n", "            if len(user_purchases_up_to_date) > 0:\n", "                if PRODUCT_NAME_COLUMN in user_purchases_up_to_date.columns:\n", "                    item_counts = user_purchases_up_to_date[PRODUCT_NAME_COLUMN].value_counts().to_dict()\n", "                else:\n", "                    # fallback to 'product_name' if present\n", "                    item_counts = user_purchases_up_to_date['product_name'].value_counts().to_dict() if 'product_name' in user_purchases_up_to_date.columns else {}\n", "                \n", "                # Only include items that were actually purchased on this day\n", "                for item in actual_items_set:\n", "                    if item in item_counts:\n", "                        actual_items_frequency[item] = item_counts[item]\n", "                    else:\n", "                        actual_items_frequency[item] = 1  # At least 1 for current purchase\n", "\n", "            # Recommended items\n", "            recs = recommendations_df[\n", "                (recommendations_df['customer_id'] == customer_id) &\n", "                (pd.to_datetime(recommendations_df['recommendation_date']) == day)\n", "            ]\n", "            recommended_items_set = set(recs['product_name']) if 'product_name' in recs.columns else set()\n", "\n", "            # k for customer (use personalized_k from recommendations_df or user_metrics_df)\n", "            k_val = None\n", "            if not recs.empty and 'personalized_k' in recs.columns:\n", "                k_val = recs['personalized_k'].iloc[0]\n", "            elif 'personalized_k' in user_metrics_df.columns:\n", "                k_row = user_metrics_df[user_metrics_df['customer_id'] == customer_id]\n", "                if not k_row.empty:\n", "                    k_val = k_row['personalized_k'].iloc[0]\n", "\n", "            output_rows.append({\n", "                'customer_id': customer_id,\n", "                # 'customer_segment': segment,\n", "                'delivery_date': day.strftime('%Y-%m-%d'),\n", "                'actual_purchased_items': actual_items_frequency,  # Now a dictionary with item: frequency\n", "                'recommended_items': list(recommended_items_set),\n", "                'k': k_val\n", "            })\n", "\n", "    # 5. Create DataFrame and save\n", "    segment_weekly_df = pd.DataFrame(output_rows)\n", "    segment_weekly_df.to_csv('user_segment_weekly_comparison.csv', index=False)\n", "    print(f\"\\n✅ Created user segment weekly comparison dataframe with frequency counts ({len(segment_weekly_df)} rows)\")\n", "    print(f\"   📊 Actual purchased items now contain frequency counts from June 1st, 2025 onwards\")\n", "    print(f\"   🗑️  Removed {len(selected_users) * 7 - len(segment_weekly_df)} rows where users had no purchases\")\n", "else:\n", "    print(\"⚠️ user_metrics_df or recommendations_df not found. Please run the evaluation first.\")\n", "\n", "print(\"\\n\" + \"=\"*70)\n", "print(\"🎉 INTELLIGENT TEMPORAL PATTERN ANALYSIS SYSTEM READY!\")\n", "print(\"🧠 Key Features:\")\n", "print(\"   ✅ Individual user-item purchase pattern analysis\")\n", "print(\"   ✅ Intelligent temporal filtering (daily, weekly, monthly, irregular)\")\n", "print(\"   ✅ Pattern-based recommendation timing\")\n", "print(\"   ✅ Confidence scoring based on purchase consistency\")\n", "print(\"   ✅ Overdue item detection for high-priority recommendations\")\n", "print(\"=\"*70)\n", "\n", "# Helpers.save_output_dataset(context=context, output_name='last_one_week_recommendations-dynamick_d1', data_frame=recommendations_df)\n", "# Helpers.save_output_plotly_chart_as_json(context=context, chart_title='recall-distribution-dynamick_d1', plotly_fig=fig_1, group=None)\n", "# Helpers.save_output_plotly_chart_as_json(context=context, chart_title='recommendation-score-distribution-dynamick_d1', plotly_fig=fig_2, group=None)\n", "# Helpers.save_output_dataset(context=context, output_name='user_metrics-dynamick_d1', data_frame=user_metrics_df)\n", "# Helpers.save_output_dataset(context=context, output_name='segment_weekly_df_d1', data_frame=segment_weekly_df)"]}, {"cell_type": "code", "execution_count": null, "id": "9bf728a9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>delivery_date</th>\n", "      <th>actual_purchased_items</th>\n", "      <th>recommended_items</th>\n", "      <th>k</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>570fa519-af38-458e-851b-6a73ce2ff756</td>\n", "      <td>2025-07-07</td>\n", "      <td>{'<PERSON><PERSON><PERSON> Chipsona - Medium size': 3, 'Mango <PERSON>...</td>\n", "      <td>[<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (Bottle ...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>570fa519-af38-458e-851b-6a73ce2ff756</td>\n", "      <td>2025-07-08</td>\n", "      <td>{'Sprouts Mix': 4, 'Tomato Hybrid': 4, '<PERSON><PERSON><PERSON>...</td>\n", "      <td>[<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (Bottle ...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>570fa519-af38-458e-851b-6a73ce2ff756</td>\n", "      <td>2025-07-10</td>\n", "      <td>{'<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)': 1, 'Apple Washington': ...</td>\n", "      <td>[<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (Bottle ...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4bcb4707-946a-45e7-98ef-8a669f362078</td>\n", "      <td>2025-07-04</td>\n", "      <td>{'Tomato Hybrid': 22, '<PERSON><PERSON> (Sponge Gourd)': ...</td>\n", "      <td>[<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>), <PERSON>, Tomato Hybrid...</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4bcb4707-946a-45e7-98ef-8a669f362078</td>\n", "      <td>2025-07-05</td>\n", "      <td>{'Beans French': 5, 'Ginger New': 8, '<PERSON><PERSON>...</td>\n", "      <td>[<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>), <PERSON>, Tomato Hybrid...</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>546e6b32-e800-48d0-aeca-b182dd6a84d4</td>\n", "      <td>2025-07-08</td>\n", "      <td>{'Apple Fuji - Imported': 7, 'Bell Pepper Yell...</td>\n", "      <td>[<PERSON> Pepper Red, <PERSON>, Banana R...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>151</th>\n", "      <td>63728ec8-8d37-4244-bb33-6cd02c290ac0</td>\n", "      <td>2025-07-04</td>\n", "      <td>{'Baby Corn': 4, '<PERSON><PERSON><PERSON> (Pointed Gourd)': 2, ...</td>\n", "      <td>[<PERSON> (Matar), <PERSON>rapes Red Flame, Fresh M...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>63728ec8-8d37-4244-bb33-6cd02c290ac0</td>\n", "      <td>2025-07-06</td>\n", "      <td>{'Cherry Tomato Red - Round': 1, 'Beans French...</td>\n", "      <td>[<PERSON> (Matar), <PERSON>rapes Red Flame, Fresh M...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>153</th>\n", "      <td>63728ec8-8d37-4244-bb33-6cd02c290ac0</td>\n", "      <td>2025-07-07</td>\n", "      <td>{'Bhindi': 6, 'Tomato Hybrid': 4, 'Mushroom': ...</td>\n", "      <td>[<PERSON> (Matar), <PERSON>rapes Red Flame, Fresh M...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154</th>\n", "      <td>63728ec8-8d37-4244-bb33-6cd02c290ac0</td>\n", "      <td>2025-07-09</td>\n", "      <td>{'<PERSON><PERSON> (<PERSON><PERSON> Gourd) - Medium': 6, 'Grapes R...</td>\n", "      <td>[<PERSON> (Matar), <PERSON>rapes Red Flame, Fresh M...</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>155 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                              customer_id delivery_date  \\\n", "0    570fa519-af38-458e-851b-6a73ce2ff756    2025-07-07   \n", "1    570fa519-af38-458e-851b-6a73ce2ff756    2025-07-08   \n", "2    570fa519-af38-458e-851b-6a73ce2ff756    2025-07-10   \n", "3    4bcb4707-946a-45e7-98ef-8a669f362078    2025-07-04   \n", "4    4bcb4707-946a-45e7-98ef-8a669f362078    2025-07-05   \n", "..                                    ...           ...   \n", "150  546e6b32-e800-48d0-aeca-b182dd6a84d4    2025-07-08   \n", "151  63728ec8-8d37-4244-bb33-6cd02c290ac0    2025-07-04   \n", "152  63728ec8-8d37-4244-bb33-6cd02c290ac0    2025-07-06   \n", "153  63728ec8-8d37-4244-bb33-6cd02c290ac0    2025-07-07   \n", "154  63728ec8-8d37-4244-bb33-6cd02c290ac0    2025-07-09   \n", "\n", "                                actual_purchased_items  \\\n", "0    {'<PERSON><PERSON><PERSON> Chipsona - Medium size': 3, '<PERSON><PERSON>...   \n", "1    {'Sprouts Mix': 4, 'Tomato Hybrid': 4, '<PERSON><PERSON><PERSON>...   \n", "2    {'<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)': 1, 'Apple Washington': ...   \n", "3    {'Tomato Hybrid': 22, '<PERSON><PERSON> (Sponge Gourd)': ...   \n", "4    {'Beans French': 5, '<PERSON>': 8, '<PERSON><PERSON> H...   \n", "..                                                 ...   \n", "150  {'Apple Fuji - Imported': 7, 'Bell Pepper Yell...   \n", "151  {'Baby Corn': 4, '<PERSON><PERSON><PERSON> (Pointed Gourd)': 2, ...   \n", "152  {'Cherry Tomato Red - Round': 1, 'Beans French...   \n", "153  {'Bhindi': 6, 'Tomato Hybrid': 4, 'Mushroom': ...   \n", "154  {'<PERSON><PERSON> (<PERSON><PERSON>) - Medium': 6, '<PERSON><PERSON><PERSON> <PERSON>...   \n", "\n", "                                     recommended_items  k  \n", "0    [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (Bottle ...  3  \n", "1    [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (Bottle ...  3  \n", "2    [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (Bottle ...  3  \n", "3    [<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>), <PERSON>, <PERSON><PERSON> Hybrid...  8  \n", "4    [<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>), <PERSON>, <PERSON><PERSON> Hybrid...  8  \n", "..                                                 ... ..  \n", "150  [<PERSON> Pepper Red, <PERSON>, Banana R...  3  \n", "151  [<PERSON> (Matar), <PERSON><PERSON><PERSON>, Fresh M...  3  \n", "152  [<PERSON> (Matar), <PERSON><PERSON><PERSON>, Fresh M...  3  \n", "153  [<PERSON> (Matar), <PERSON><PERSON><PERSON> Flame, Fresh M...  3  \n", "154  [<PERSON> (Matar), <PERSON><PERSON><PERSON>, Fresh M...  3  \n", "\n", "[155 rows x 5 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["segment_weekly_df\n"]}, {"cell_type": "code", "execution_count": null, "id": "4c09978a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}