#!/usr/bin/env python3
"""
Verify that the date matching logic in segment_weekly_df is working correctly
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def verify_date_matching():
    """Verify date matching logic"""
    
    print("🔍 Verifying Date Matching Logic in segment_weekly_df")
    print("=" * 60)
    
    try:
        # Load the CSV file to check actual data
        df = pd.read_csv('user_segment_weekly_comparison.csv')
        
        print(f"📊 Loaded segment_weekly_df:")
        print(f"   - Shape: {df.shape}")
        print(f"   - Columns: {list(df.columns)}")
        
        # Check date range
        df['delivery_date'] = pd.to_datetime(df['delivery_date'])
        print(f"   - Date range: {df['delivery_date'].min()} to {df['delivery_date'].max()}")
        print(f"   - Unique dates: {df['delivery_date'].nunique()}")
        
        # Check for users with identical recommendations across dates
        print(f"\n🔍 Analyzing recommendation patterns:")
        
        identical_patterns = []
        
        for customer_id in df['customer_id'].unique()[:10]:  # Check first 10 users
            user_data = df[df['customer_id'] == customer_id].sort_values('delivery_date')
            
            if len(user_data) > 1:
                # Check for consecutive identical recommendations
                prev_recs = None
                identical_count = 0
                
                for _, row in user_data.iterrows():
                    current_recs = row['recommended_items']
                    
                    if prev_recs is not None and prev_recs == current_recs:
                        identical_count += 1
                        identical_patterns.append({
                            'customer_id': customer_id,
                            'date': row['delivery_date'],
                            'recommendations': current_recs
                        })
                    
                    prev_recs = current_recs
                
                if identical_count > 0:
                    print(f"   👤 {customer_id[:8]}...: {identical_count} identical consecutive recommendations")
                else:
                    print(f"   👤 {customer_id[:8]}...: All recommendations different ✅")
        
        print(f"\n📊 Summary:")
        print(f"   - Total identical patterns found: {len(identical_patterns)}")
        
        if len(identical_patterns) > 0:
            print(f"   - Sample identical patterns:")
            for pattern in identical_patterns[:3]:
                print(f"     {pattern['date']}: {pattern['recommendations']}")
        
        # Check if this could be due to empty recommendations
        empty_recs = df[df['recommended_items'] == '[]']
        print(f"   - Empty recommendations: {len(empty_recs)} out of {len(df)} ({len(empty_recs)/len(df)*100:.1f}%)")
        
        # Check recommendation diversity
        all_recs = df['recommended_items'].value_counts()
        print(f"   - Unique recommendation sets: {len(all_recs)}")
        print(f"   - Most common recommendation set appears {all_recs.iloc[0]} times")
        
        # Show most common recommendation patterns
        print(f"\n🔍 Most common recommendation patterns:")
        for i, (recs, count) in enumerate(all_recs.head(5).items()):
            print(f"   {i+1}. {recs} (appears {count} times)")
        
        # Check if the issue is with specific users or dates
        print(f"\n🔍 Analysis by date:")
        for date in sorted(df['delivery_date'].unique()):
            date_data = df[df['delivery_date'] == date]
            unique_recs = date_data['recommended_items'].nunique()
            total_users = len(date_data)
            print(f"   {date.strftime('%Y-%m-%d')}: {unique_recs} unique recommendation sets for {total_users} users")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_date_matching()
