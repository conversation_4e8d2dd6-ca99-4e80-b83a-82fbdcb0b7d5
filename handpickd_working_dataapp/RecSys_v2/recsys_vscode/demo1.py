# # -------------------------------------------------------------------------------- NOTEBOOK-CELL: CODE
# # Required imports

from utils.notebookhelpers.helpers import Helpers
from utils.dtos.templateOutputCollection import TemplateOutputCollection
from utils.dtos.templateOutput import TemplateOutput
from utils.dtos.templateOutput import OutputType
from utils.dtos.templateOutput import ChartType
from utils.dtos.variable import Metadata
from utils.rcclient.commons.variable_datatype import VariableDatatype
from utils.dtos.templateOutput import FileType
from utils.dtos.rc_ml_model import RCMLModel
from utils.notebookhelpers.helpers import Helpers
from utils.libutils.vectorStores.utils import VectorStoreUtils

context = Helpers.getOrCreateContext(contextId='contextId', localVars=locals())

# 
import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import time
import hashlib
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import plotly.graph_objects as go
import plotly.express as px
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')


from utils.notebookhelpers.helpers import Helpers
context = Helpers.getOrCreateContext(contextId='contextId', localVars=locals())
catalogue_rc = Helpers.getEntityData(context, 'catalogue_rc_with_parent_names')
customer_orders_rc = Helpers.getEntityData(context, 'HighFrequencyCustomerOrders')
repurchase_ratios_df = Helpers.getEntityData(context, 'repurchase_ratios')
# catalogue_rc = pd.read_csv('catalogue_rc_with_parent_names.csv')
# customer_orders_rc = pd.read_csv('customer_orders.csv')
# repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')

class HighPerformanceStableRecommendationSystem:
    def __init__(self, user_repurchase_ratios_df=None, product_name_column='name'):
        """
        Initialize the recommendation system with configurable product name column

        Args:
            user_repurchase_ratios_df: DataFrame with user repurchase ratios
            product_name_column: Column name to use for product identification ('name' or 'sku_parent_name')
        """
        # Configuration
        self.product_name_column = product_name_column
        print(f"🔧 Using '{product_name_column}' column for product identification")

        # Pre-computed static data (computed once) - now using configurable product names
        self.item_categories = {}
        self.item_purchase_frequency = {}
        self.item_avg_quantities = {}
        self.catalogue_lookup = {}
        self.name_to_sku_mapping = {}  # Map product names to available SKU codes
        self.sku_to_name_mapping = {}  # Map SKU codes to product names

        # Smart caching system
        self.cache = {}
        self.popular_items = []

        # User-specific repurchase ratios
        self.user_repurchase_ratios = {}
        self.default_repurchase_ratio = 0.7  # Fallback for users not in the dataset

        # 🚀 HIGH-PERFORMANCE STABLE RECOMMENDATION SYSTEM
        # Restore proven ensemble approach with gentle stability improvements

        # 🎯 GENTLE STABILITY FEATURES (not aggressive deduplication)
        self.gentle_variety_enabled = True  # Enable gentle variety without blocking core items
        self.performance_smoothing_enabled = True  # Enable performance smoothing
        self.stability_boost_factor = 1.2  # Gentle boost (not aggressive 1.5)

        # 🌟 INTELLIGENT DIVERSITY FEATURES
        self.diversity_heuristic_enabled = True  # Enable intelligent diversity heuristic
        self.daily_essential_threshold = 0.20  # Items purchased >= 20% of time are daily essentials (more items qualify)
        self.weekly_regular_threshold = 0.05   # Items purchased 5-20% of time are weekly regulars (more items qualify)
        self.diversity_rotation_strength = 0.5  # Strength of diversity rotation (0.5 = 50% of recommendations can be rotated)

        # 🎯 PERFORMANCE-DIVERSITY BALANCE (ADAPTIVE BASED ON USER TYPE)
        self.core_performance_ratio = 0.5      # Default 50% core items for performance
        self.diversity_ratio = 0.5             # Default 50% diversity items for variety

        # 🚀 ADAPTIVE RATIOS FOR HIGH-PERFORMANCE USERS
        self.high_repurchase_threshold = 0.8   # Users with ≥80% repurchase ratio
        self.high_repurchase_core_ratio = 0.7  # 70% core items for high-repurchase users
        self.high_repurchase_diversity_ratio = 0.3  # 30% diversity for high-repurchase users

        # 🌟 STABLE VARIETY PARAMETERS
        self.variety_rotation_days = 3  # 3-day rotation cycle for stable variety

        # 🎯 K-AWARE DIVERSITY PARAMETERS
        self.k_diversity_scaling = True  # Enable k-aware diversity scaling
        self.min_diversity_ratio = 0.3   # Minimum 30% diversity for small k
        self.max_diversity_ratio = 0.7   # Maximum 70% diversity for large k
        self.k_scaling_threshold = 5     # Start scaling diversity for k > 5

        # 🔄 DAILY ROTATION ENFORCEMENT PARAMETERS
        self.daily_rotation_enforcement = True  # Enable aggressive daily rotation
        self.min_daily_change_ratio = 0.3      # Minimum 30% items must change daily
        self.max_daily_change_ratio = 0.6      # Maximum 60% items can change daily
        self.rotation_memory_days = 3          # Remember last 3 days for rotation

        # 📊 PERFORMANCE TRACKING
        self.recommendation_history = {}  # Track recommendations for analysis
        self.daily_performance_buffer = {}  # Buffer to smooth daily variations
        self.daily_rotation_history = {}  # Track recent recommendations for rotation enforcement

        # 🔧 COMPATIBILITY ATTRIBUTES (for existing methods)
        self.analysis_window_days = 14  # Analyze last 14 days for daily patterns
        self.user_daily_essentials = {}  # {user_id: {item: daily_frequency_score}}
        self.user_last_recommended = {}  # {user_id: {item: last_recommended_date}}
        self.recommendation_cooldown_days = 7  # Much gentler cooldown (7 days instead of 2)
        self.daily_essential_threshold = 0.3  # Lower threshold for more flexibility
        self.deduplication_enabled = False  # Disable aggressive deduplication
        self.pattern_aware_enabled = True  # Enable pattern-aware features
        self.user_purchase_patterns = {}  # {user_id: pattern_analysis}
        self.user_category_rotation = {}  # {user_id: category_rotation_pattern}
        self.user_day_preferences = {}  # {user_id: day_of_week_preferences}

        if user_repurchase_ratios_df is not None:
            self.load_user_repurchase_ratios(user_repurchase_ratios_df)
    
    def load_user_repurchase_ratios(self, repurchase_ratios_df):
        """Load user-specific repurchase ratios from DataFrame"""
        self.user_repurchase_ratios = dict(zip(
            repurchase_ratios_df['customer_id'], 
            repurchase_ratios_df['repurchase_ratio']
        ))
        print(f"✅ Loaded dynamic repurchase ratios for {len(self.user_repurchase_ratios):,} users")
        if len(self.user_repurchase_ratios) > 0:
            avg_ratio = repurchase_ratios_df['repurchase_ratio'].mean()
            print(f"   Average repurchase ratio: {avg_ratio:.3f}")
    
    def get_user_repurchase_ratio(self, user_id):
        """Get repurchase ratio for a specific user"""
        return self.user_repurchase_ratios.get(user_id, self.default_repurchase_ratio)

    def analyze_daily_purchase_patterns(self, customer_orders_df, current_date):
        """
        🧠 INTELLIGENT DAILY PURCHASE PATTERN ANALYSIS

        Analyzes each user's historical purchasing patterns to identify:
        1. Daily essential items (purchased 80%+ of days in analysis window)
        2. Purchase frequency patterns for each user-item pair
        3. Recommendation cooldown tracking

        Args:
            customer_orders_df: DataFrame with customer orders
            current_date: Current evaluation date for analysis
        """
        print(f"🧠 Analyzing daily purchase patterns for {current_date.strftime('%Y-%m-%d')}...")

        # Convert to datetime if needed
        if not pd.api.types.is_datetime64_any_dtype(customer_orders_df['delivery_date']):
            customer_orders_df = customer_orders_df.copy()
            customer_orders_df['delivery_date'] = pd.to_datetime(customer_orders_df['delivery_date'])

        # Define analysis window (last N days before current date)
        analysis_start_date = current_date - timedelta(days=self.analysis_window_days)

        # Filter orders within analysis window
        analysis_orders = customer_orders_df[
            (customer_orders_df['delivery_date'] >= analysis_start_date) &
            (customer_orders_df['delivery_date'] < current_date)
        ].copy()

        if len(analysis_orders) == 0:
            print(f"   ⚠️ No orders found in analysis window ({analysis_start_date} to {current_date})")
            return

        # Convert to product names
        analysis_orders['product_name'] = analysis_orders['sku_code'].map(self.sku_to_name_mapping)
        analysis_orders = analysis_orders.dropna(subset=['product_name'])

        print(f"   📊 Analyzing {len(analysis_orders)} orders from {analysis_start_date.strftime('%Y-%m-%d')} to {current_date.strftime('%Y-%m-%d')}")

        # Create date range for analysis
        date_range = pd.date_range(start=analysis_start_date, end=current_date - timedelta(days=1), freq='D')

        # Analyze each user's purchase patterns
        daily_essentials_found = 0
        users_analyzed = 0

        for user_id in analysis_orders['customer_id'].unique():
            user_orders = analysis_orders[analysis_orders['customer_id'] == user_id]

            # Create user's daily purchase matrix
            user_daily_purchases = {}
            for date in date_range:
                date_orders = user_orders[user_orders['delivery_date'] == date]
                purchased_items = set(date_orders['product_name']) if len(date_orders) > 0 else set()
                user_daily_purchases[date] = purchased_items

            # Calculate purchase frequency for each item
            user_item_frequencies = {}
            for product_name in user_orders['product_name'].unique():
                # Count how many days this item was purchased
                purchase_days = sum(1 for date_items in user_daily_purchases.values() if product_name in date_items)
                frequency_score = purchase_days / len(date_range)
                user_item_frequencies[product_name] = frequency_score

            # Identify daily essentials (items purchased frequently)
            daily_essentials = {
                item: freq for item, freq in user_item_frequencies.items()
                if freq >= self.daily_essential_threshold
            }

            # Store results
            self.user_daily_essentials[user_id] = daily_essentials

            if daily_essentials:
                daily_essentials_found += len(daily_essentials)

            users_analyzed += 1

        print(f"   ✅ Analyzed {users_analyzed} users")
        print(f"   🎯 Found {daily_essentials_found} daily essential items across all users")
        print(f"   📈 Average daily essentials per user: {daily_essentials_found/users_analyzed:.1f}")

        # Show sample daily essentials
        sample_users = list(self.user_daily_essentials.keys())[:3]
        for user_id in sample_users:
            essentials = self.user_daily_essentials[user_id]
            if essentials:
                print(f"   👤 Sample user {user_id[:8]}...: {len(essentials)} daily essentials")
                for item, freq in list(essentials.items())[:3]:
                    print(f"      • {item}: {freq:.1%} purchase frequency")

    def is_item_in_cooldown(self, user_id, item_name, current_date):
        """
        Check if an item is in recommendation cooldown period for a user

        Args:
            user_id: User ID
            item_name: Product name
            current_date: Current evaluation date

        Returns:
            bool: True if item is in cooldown, False otherwise
        """
        if user_id not in self.user_last_recommended:
            return False

        if item_name not in self.user_last_recommended[user_id]:
            return False

        last_recommended = self.user_last_recommended[user_id][item_name]
        days_since_recommended = (current_date - last_recommended).days

        return days_since_recommended < self.recommendation_cooldown_days

    def is_daily_essential(self, user_id, item_name):
        """
        Check if an item is classified as a daily essential for a user

        Args:
            user_id: User ID
            item_name: Product name

        Returns:
            bool: True if item is a daily essential, False otherwise
        """
        if user_id not in self.user_daily_essentials:
            return False

        return item_name in self.user_daily_essentials[user_id]

    def update_recommendation_tracking(self, user_id, item_name, recommendation_date):
        """
        Update the last recommended date for a user-item pair

        Args:
            user_id: User ID
            item_name: Product name
            recommendation_date: Date when item was recommended
        """
        if user_id not in self.user_last_recommended:
            self.user_last_recommended[user_id] = {}

        self.user_last_recommended[user_id][item_name] = recommendation_date

    def analyze_user_purchase_patterns(self, customer_orders_df, current_date):
        """
        🧠 ENHANCED PATTERN-AWARE ANALYSIS

        Analyzes user's contextual purchase patterns:
        1. Day-of-week shopping patterns
        2. Category rotation patterns
        3. Purchase quantity patterns
        4. Seasonal/temporal preferences

        Args:
            customer_orders_df: DataFrame with customer orders
            current_date: Current evaluation date
        """
        print(f"🧠 Analyzing user purchase patterns for {current_date.strftime('%Y-%m-%d')}...")

        # Convert to datetime if needed
        if not pd.api.types.is_datetime64_any_dtype(customer_orders_df['delivery_date']):
            customer_orders_df = customer_orders_df.copy()
            customer_orders_df['delivery_date'] = pd.to_datetime(customer_orders_df['delivery_date'])

        # Define analysis window (last 21 days for better pattern detection)
        analysis_start_date = current_date - timedelta(days=21)

        # Filter orders within analysis window
        analysis_orders = customer_orders_df[
            (customer_orders_df['delivery_date'] >= analysis_start_date) &
            (customer_orders_df['delivery_date'] < current_date)
        ].copy()

        if len(analysis_orders) == 0:
            print(f"   ⚠️ No orders found in pattern analysis window")
            return

        # Convert to product names
        analysis_orders['product_name'] = analysis_orders['sku_code'].map(self.sku_to_name_mapping)
        analysis_orders = analysis_orders.dropna(subset=['product_name'])

        # Add day of week and other temporal features
        analysis_orders['day_of_week'] = analysis_orders['delivery_date'].dt.day_name()
        analysis_orders['week_number'] = analysis_orders['delivery_date'].dt.isocalendar().week

        print(f"   📊 Analyzing {len(analysis_orders)} orders from {analysis_start_date.strftime('%Y-%m-%d')}")

        patterns_found = 0

        for user_id in analysis_orders['customer_id'].unique():
            user_orders = analysis_orders[analysis_orders['customer_id'] == user_id]

            if len(user_orders) < 3:  # Need minimum orders for pattern analysis
                continue

            # 1. Day-of-week patterns
            day_patterns = {}
            for day in user_orders['day_of_week'].unique():
                day_orders = user_orders[user_orders['day_of_week'] == day]
                day_categories = [self.item_categories.get(item, 'Unknown') for item in day_orders['product_name']]
                day_patterns[day] = {
                    'item_count': len(day_orders),
                    'unique_items': len(day_orders['product_name'].unique()),
                    'top_categories': pd.Series(day_categories).value_counts().head(3).to_dict(),
                    'avg_quantity': day_orders['ordered_qty'].mean()
                }

            # 2. Category rotation patterns
            weekly_categories = []
            for week in user_orders['week_number'].unique():
                week_orders = user_orders[user_orders['week_number'] == week]
                week_categories = [self.item_categories.get(item, 'Unknown') for item in week_orders['product_name']]
                weekly_categories.append(pd.Series(week_categories).value_counts().head(3).index.tolist())

            # 3. Purchase quantity patterns
            quantity_pattern = {
                'avg_items_per_order': user_orders.groupby('delivery_date')['product_name'].count().mean(),
                'bulk_shopping_days': len(user_orders.groupby('delivery_date').filter(lambda x: len(x) > 5)),
                'light_shopping_days': len(user_orders.groupby('delivery_date').filter(lambda x: len(x) <= 2))
            }

            # Store patterns
            self.user_purchase_patterns[user_id] = {
                'day_patterns': day_patterns,
                'quantity_pattern': quantity_pattern,
                'total_orders': len(user_orders),
                'analysis_period': (analysis_start_date, current_date)
            }

            self.user_category_rotation[user_id] = weekly_categories

            # Determine current day preference
            current_day = current_date.strftime('%A')
            self.user_day_preferences[user_id] = day_patterns.get(current_day, {})

            patterns_found += 1

        print(f"   ✅ Analyzed patterns for {patterns_found} users")
        print(f"   📈 Found day-of-week patterns, category rotations, and quantity preferences")

        # Show sample patterns
        sample_users = list(self.user_purchase_patterns.keys())[:2]
        for user_id in sample_users:
            patterns = self.user_purchase_patterns[user_id]
            print(f"   👤 Sample user {user_id[:8]}...: {patterns['total_orders']} orders")
            if patterns['day_patterns']:
                top_day = max(patterns['day_patterns'].items(), key=lambda x: x[1]['item_count'])
                print(f"      📅 Most active day: {top_day[0]} ({top_day[1]['item_count']} items)")
                if top_day[1]['top_categories']:
                    top_cat = list(top_day[1]['top_categories'].keys())[0]
                    print(f"      🛒 Preferred category on {top_day[0]}: {top_cat}")

    def get_alternative_recommendations(self, user_id, blocked_item, user_profile, similarity_dict, num_alternatives=3):
        """
        🔄 INTELLIGENT ALTERNATIVE RECOMMENDATION STRATEGIES

        When an item is blocked due to cooldown, find intelligent alternatives:
        1. Similar items from the same category
        2. Complementary items that pair well with recent purchases
        3. Discovery items from frequently shopped categories
        4. Collaborative filtering alternatives

        Args:
            user_id: User ID
            blocked_item: Item that's blocked due to cooldown
            user_profile: User's purchase profile
            similarity_dict: Item similarity matrix
            num_alternatives: Number of alternatives to generate

        Returns:
            list: Alternative recommendation items
        """
        alternatives = []
        blocked_category = self.item_categories.get(blocked_item, 'Unknown')

        # Strategy 1: Similar items from same category
        if blocked_category != 'Unknown':
            category_items = [
                item for item, category in self.item_categories.items()
                if category == blocked_category and item != blocked_item
                and item not in user_profile.get('purchased_items', [])
                and not self.is_item_in_cooldown(user_id, item, pd.Timestamp.now())
            ]

            # Sort by popularity and take top candidates
            category_alternatives = sorted(
                category_items,
                key=lambda x: self.item_purchase_frequency.get(x, 0),
                reverse=True
            )[:num_alternatives]

            alternatives.extend(category_alternatives)

        # Strategy 2: Collaborative filtering alternatives
        if blocked_item in similarity_dict:
            similar_items = [
                item for item, sim_score in similarity_dict[blocked_item].items()
                if item not in user_profile.get('purchased_items', [])
                and not self.is_item_in_cooldown(user_id, item, pd.Timestamp.now())
                and sim_score > 0.1  # Minimum similarity threshold
            ]

            # Sort by similarity score
            collab_alternatives = sorted(
                similar_items,
                key=lambda x: similarity_dict[blocked_item].get(x, 0),
                reverse=True
            )[:num_alternatives]

            alternatives.extend(collab_alternatives)

        # Strategy 3: Discovery items from user's preferred categories
        user_categories = user_profile.get('category_preferences', {})
        for category, preference in sorted(user_categories.items(), key=lambda x: x[1], reverse=True)[:3]:
            if preference > 0.1:  # User shows interest in this category
                category_discovery = [
                    item for item in self.popular_items
                    if self.item_categories.get(item) == category
                    and item not in user_profile.get('purchased_items', [])
                    and not self.is_item_in_cooldown(user_id, item, pd.Timestamp.now())
                ][:2]  # Take top 2 from each preferred category

                alternatives.extend(category_discovery)

        # Remove duplicates and limit to requested number
        unique_alternatives = []
        seen = set()
        for item in alternatives:
            if item not in seen:
                unique_alternatives.append(item)
                seen.add(item)
                if len(unique_alternatives) >= num_alternatives:
                    break

        return unique_alternatives[:num_alternatives]

    def get_pattern_aware_alternatives(self, user_id, blocked_items, user_profile, similarity_dict, current_date, num_needed):
        """
        🧠 PATTERN-AWARE ALTERNATIVE GENERATION

        Generate alternatives based on user's purchase patterns and day-of-week preferences.
        This ensures we have enough recommendations while respecting user behavior patterns.

        Args:
            user_id: User ID
            blocked_items: List of (item_name, rec) tuples that were blocked
            user_profile: User's purchase profile
            similarity_dict: Item similarity matrix
            current_date: Current evaluation date
            num_needed: Number of alternatives needed

        Returns:
            list: Pattern-aware alternative recommendations
        """
        alternatives = []
        blocked_item_names = [item_name for item_name, _ in blocked_items]
        existing_items = set(blocked_item_names)

        # Get user's day-of-week preferences
        current_day = current_date.strftime('%A')
        day_preferences = self.user_day_preferences.get(user_id, {})
        preferred_categories = list(day_preferences.get('top_categories', {}).keys()) if day_preferences else ['Vegetables', 'Fruits']

        # Debug logging
        debug_user = user_id.startswith('570fa519') or user_id.startswith('4bcb4707')
        if debug_user:
            print(f"      🔄 Generating {num_needed} pattern-aware alternatives for {current_day}")
            print(f"         Preferred categories: {preferred_categories[:2]}")

        # Strategy 1: Items from preferred categories for this day
        for category in preferred_categories[:2]:  # Top 2 preferred categories
            if len(alternatives) >= num_needed:
                break

            category_items = [
                item for item in self.popular_items
                if self.item_categories.get(item) == category
                and item not in existing_items
                and item not in user_profile.get('purchased_items', [])
                and not self.is_item_in_cooldown(user_id, item, current_date)
            ]

            # Sort by popularity and add top items
            for item in category_items[:3]:  # Top 3 from each category
                if len(alternatives) >= num_needed:
                    break

                item_info = self.catalogue_lookup.get(item, {'sku_codes': ['UNKNOWN'], 'category_name': 'Unknown'})
                alt_rec = {
                    'sku_code': item_info['sku_codes'][0],
                    'product_name': item,
                    'category': item_info.get('category_name', 'Unknown'),
                    'score': 0.5,  # Moderate score for pattern-aware alternatives
                    'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                    'recommendation_type': 'pattern_aware_alternative',
                    'strategy': f'day_preference_{current_day}_{category}',
                    'pattern_boost': f'day_category_{category}'
                }
                alternatives.append(alt_rec)
                existing_items.add(item)

                if debug_user:
                    print(f"         ➕ Added {item} from {category} (day preference)")

        # Strategy 2: Similar items to blocked items (but not in cooldown)
        if len(alternatives) < num_needed:
            for blocked_item, original_rec in blocked_items[:3]:  # Check top 3 blocked items
                if len(alternatives) >= num_needed:
                    break

                if blocked_item in similarity_dict:
                    similar_items = [
                        item for item, score in similarity_dict[blocked_item].items()
                        if score > 0.3
                        and item not in existing_items
                        and not self.is_item_in_cooldown(user_id, item, current_date)
                    ]

                    for item in similar_items[:2]:  # Top 2 similar items
                        if len(alternatives) >= num_needed:
                            break

                        item_info = self.catalogue_lookup.get(item, {'sku_codes': ['UNKNOWN'], 'category_name': 'Unknown'})
                        alt_rec = {
                            'sku_code': item_info['sku_codes'][0],
                            'product_name': item,
                            'category': item_info.get('category_name', 'Unknown'),
                            'score': max(0.4, original_rec['score'] * 0.8),
                            'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                            'recommendation_type': 'similarity_alternative',
                            'strategy': f'similar_to_{blocked_item}',
                            'replaced_item': blocked_item
                        }
                        alternatives.append(alt_rec)
                        existing_items.add(item)

                        if debug_user:
                            print(f"         ➕ Added {item} (similar to {blocked_item})")

        # Strategy 3: Popular items from user's historical categories (if still need more)
        if len(alternatives) < num_needed:
            user_categories = list(user_profile.get('category_preferences', {}).keys())[:3]

            for category in user_categories:
                if len(alternatives) >= num_needed:
                    break

                category_items = [
                    item for item in self.popular_items
                    if self.item_categories.get(item) == category
                    and item not in existing_items
                    and not self.is_item_in_cooldown(user_id, item, current_date)
                ]

                for item in category_items[:2]:  # Top 2 from each historical category
                    if len(alternatives) >= num_needed:
                        break

                    item_info = self.catalogue_lookup.get(item, {'sku_codes': ['UNKNOWN'], 'category_name': 'Unknown'})
                    alt_rec = {
                        'sku_code': item_info['sku_codes'][0],
                        'product_name': item,
                        'category': item_info.get('category_name', 'Unknown'),
                        'score': 0.4,
                        'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                        'recommendation_type': 'historical_category_alternative',
                        'strategy': f'historical_{category}',
                        'pattern_boost': f'historical_category_{category}'
                    }
                    alternatives.append(alt_rec)
                    existing_items.add(item)

                    if debug_user:
                        print(f"         ➕ Added {item} from historical category {category}")

        if debug_user:
            print(f"         🎯 Generated {len(alternatives)} pattern-aware alternatives")

        return alternatives[:num_needed]

    def build_stable_user_profile(self, user_id, user_profile, current_date):
        """
        🚀 BUILD STABLE USER PREFERENCE PROFILE

        Creates a stable, consistent user profile that captures long-term preferences
        and reduces volatility in recommendations.

        Args:
            user_id: User ID
            user_profile: Original user profile
            current_date: Current evaluation date

        Returns:
            dict: Stable user preference profile
        """

        # Extract core preferences with stability weighting
        purchased_items = user_profile.get('purchased_items', [])
        category_preferences = user_profile.get('category_preferences', {})

        # Calculate item frequency scores (more stable than binary preferences)
        item_frequencies = {}
        total_purchases = len(purchased_items)

        if total_purchases > 0:
            for item in set(purchased_items):
                frequency = purchased_items.count(item) / total_purchases
                if frequency >= self.min_purchase_frequency:  # Only include frequently purchased items
                    item_frequencies[item] = frequency

        # Identify core items (top 70% by frequency)
        sorted_items = sorted(item_frequencies.items(), key=lambda x: x[1], reverse=True)
        core_item_count = max(1, int(len(sorted_items) * 0.7))
        core_items = dict(sorted_items[:core_item_count])

        # Identify variety items (remaining 30%)
        variety_items = dict(sorted_items[core_item_count:])

        # Calculate category stability scores
        stable_categories = {}
        for category, preference in category_preferences.items():
            if preference > 0.1:  # Only include categories with meaningful preference
                stable_categories[category] = min(preference * 1.2, 1.0)  # Boost stable categories

        stable_profile = {
            'core_items': core_items,
            'variety_items': variety_items,
            'stable_categories': stable_categories,
            'total_purchases': total_purchases,
            'user_type': self.classify_user_stability(user_id, item_frequencies),
            'stability_score': self.calculate_user_stability_score(item_frequencies)
        }

        return stable_profile

    def classify_user_stability(self, user_id, item_frequencies):
        """Classify user based on purchase pattern stability"""
        if not item_frequencies:
            return 'new_user'

        # Calculate frequency distribution
        frequencies = list(item_frequencies.values())
        max_freq = max(frequencies)
        avg_freq = sum(frequencies) / len(frequencies)

        if max_freq > 0.4:  # User has dominant items
            return 'stable_user'
        elif avg_freq > 0.2:  # User has consistent preferences
            return 'regular_user'
        else:  # User has diverse preferences
            return 'exploratory_user'

    def calculate_user_stability_score(self, item_frequencies):
        """Calculate a stability score for the user (0-1, higher = more stable)"""
        if not item_frequencies:
            return 0.5  # Neutral for new users

        frequencies = list(item_frequencies.values())
        # Higher variance in frequencies = more stable (user has clear preferences)
        if len(frequencies) > 1:
            variance = np.var(frequencies)
            stability = min(variance * 10, 1.0)  # Scale to 0-1
        else:
            stability = 0.8  # Single item users are very stable

        return stability

    def generate_stable_core_recommendations(self, user_id, stable_profile, similarity_dict, current_date, core_count, repurchase_ratio):
        """
        🎯 GENERATE STABLE CORE RECOMMENDATIONS

        Generates the core 70% of recommendations from user's most preferred items.
        These provide stability and high performance.

        Args:
            user_id: User ID
            stable_profile: Stable user preference profile
            similarity_dict: Item similarity matrix
            current_date: Current evaluation date
            core_count: Number of core recommendations to generate
            repurchase_ratio: User's repurchase ratio

        Returns:
            list: Stable core recommendations
        """

        core_recommendations = []
        core_items = stable_profile['core_items']
        stable_categories = stable_profile['stable_categories']
        user_type = stable_profile['user_type']

        # Strategy 1: Direct core items (highest priority)
        for item, frequency in sorted(core_items.items(), key=lambda x: x[1], reverse=True):
            if len(core_recommendations) >= core_count:
                break

            # Check if item is available in catalogue
            if item in self.catalogue_lookup:
                item_info = self.catalogue_lookup[item]

                # Calculate stable score based on frequency and user type
                base_score = frequency * self.stability_boost_factor

                # Boost score based on user type
                if user_type == 'stable_user':
                    base_score *= 1.3
                elif user_type == 'regular_user':
                    base_score *= 1.1

                # Apply repurchase ratio boost
                if repurchase_ratio and repurchase_ratio > 0.5:
                    base_score *= (1 + repurchase_ratio * 0.3)

                core_rec = {
                    'sku_code': item_info['sku_codes'][0],
                    'product_name': item,
                    'category': item_info.get('category_name', 'Unknown'),
                    'score': min(base_score, 1.0),  # Cap at 1.0
                    'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                    'recommendation_type': 'stable_core',
                    'strategy': f'core_item_{user_type}',
                    'frequency': frequency,
                    'stability_boost': True
                }

                core_recommendations.append(core_rec)

        # Strategy 2: Similar items to core items (if need more)
        if len(core_recommendations) < core_count:
            needed = core_count - len(core_recommendations)
            existing_items = set(r['product_name'] for r in core_recommendations)

            for core_item in list(core_items.keys())[:3]:  # Top 3 core items
                if len(core_recommendations) >= core_count:
                    break

                if core_item in similarity_dict:
                    similar_items = [
                        (item, score) for item, score in similarity_dict[core_item].items()
                        if score > 0.4 and item not in existing_items and item in self.catalogue_lookup
                    ]

                    for item, sim_score in sorted(similar_items, key=lambda x: x[1], reverse=True)[:2]:
                        if len(core_recommendations) >= core_count:
                            break

                        item_info = self.catalogue_lookup[item]

                        # Score based on similarity to core item
                        base_score = sim_score * 0.8 * self.stability_boost_factor

                        similar_rec = {
                            'sku_code': item_info['sku_codes'][0],
                            'product_name': item,
                            'category': item_info.get('category_name', 'Unknown'),
                            'score': base_score,
                            'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                            'recommendation_type': 'stable_core_similar',
                            'strategy': f'similar_to_core_{core_item}',
                            'similarity_score': sim_score,
                            'stability_boost': True
                        }

                        core_recommendations.append(similar_rec)
                        existing_items.add(item)

        # Strategy 3: Popular items from stable categories (if still need more)
        if len(core_recommendations) < core_count:
            existing_items = set(r['product_name'] for r in core_recommendations)

            for category, preference in sorted(stable_categories.items(), key=lambda x: x[1], reverse=True):
                if len(core_recommendations) >= core_count:
                    break

                category_items = [
                    item for item in self.popular_items[:20]  # Top 20 popular items
                    if self.item_categories.get(item) == category
                    and item not in existing_items
                    and item in self.catalogue_lookup
                ]

                for item in category_items[:2]:  # Top 2 from each stable category
                    if len(core_recommendations) >= core_count:
                        break

                    item_info = self.catalogue_lookup[item]

                    # Score based on category preference and popularity
                    base_score = preference * 0.7 * self.stability_boost_factor

                    category_rec = {
                        'sku_code': item_info['sku_codes'][0],
                        'product_name': item,
                        'category': item_info.get('category_name', 'Unknown'),
                        'score': base_score,
                        'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                        'recommendation_type': 'stable_core_category',
                        'strategy': f'stable_category_{category}',
                        'category_preference': preference,
                        'stability_boost': True
                    }

                    core_recommendations.append(category_rec)
                    existing_items.add(item)

        return core_recommendations[:core_count]

    def generate_stable_variety_recommendations(self, user_id, stable_profile, similarity_dict, current_date, variety_count, core_recommendations):
        """
        🌟 GENERATE STABLE VARIETY RECOMMENDATIONS

        Generates the variety 30% of recommendations with gentle rotation.
        These provide discovery while maintaining stability.

        Args:
            user_id: User ID
            stable_profile: Stable user preference profile
            similarity_dict: Item similarity matrix
            current_date: Current evaluation date
            variety_count: Number of variety recommendations to generate
            core_recommendations: Already generated core recommendations

        Returns:
            list: Stable variety recommendations
        """

        variety_recommendations = []
        existing_items = set(r['product_name'] for r in core_recommendations)
        variety_items = stable_profile['variety_items']
        stable_categories = stable_profile['stable_categories']

        # Calculate rotation day (gentle 3-day rotation)
        rotation_day = (current_date.toordinal() // self.variety_rotation_days) % 3

        # Strategy 1: Rotated variety items from user's purchase history
        variety_item_list = list(variety_items.items())
        if variety_item_list:
            # Rotate through variety items based on rotation day
            items_per_rotation = max(1, len(variety_item_list) // 3)
            start_idx = rotation_day * items_per_rotation
            end_idx = start_idx + items_per_rotation

            rotated_variety_items = variety_item_list[start_idx:end_idx]
            if not rotated_variety_items and variety_item_list:  # Fallback
                rotated_variety_items = variety_item_list[:items_per_rotation]

            for item, frequency in rotated_variety_items:
                if len(variety_recommendations) >= variety_count:
                    break

                if item not in existing_items and item in self.catalogue_lookup:
                    item_info = self.catalogue_lookup[item]

                    # Score based on frequency but lower than core items
                    base_score = frequency * 0.8  # Lower than core items

                    variety_rec = {
                        'sku_code': item_info['sku_codes'][0],
                        'product_name': item,
                        'category': item_info.get('category_name', 'Unknown'),
                        'score': base_score,
                        'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                        'recommendation_type': 'stable_variety',
                        'strategy': f'variety_rotation_day_{rotation_day}',
                        'frequency': frequency,
                        'rotation_day': rotation_day
                    }

                    variety_recommendations.append(variety_rec)
                    existing_items.add(item)

        # Strategy 2: Discovery items from stable categories (gentle exploration)
        if len(variety_recommendations) < variety_count:
            category_list = list(stable_categories.items())

            for i, (category, preference) in enumerate(category_list):
                if len(variety_recommendations) >= variety_count:
                    break

                # Rotate categories based on rotation day
                if i % 3 == rotation_day:
                    category_items = [
                        item for item in self.popular_items[10:30]  # Mid-tier popular items for discovery
                        if self.item_categories.get(item) == category
                        and item not in existing_items
                        and item in self.catalogue_lookup
                    ]

                    for item in category_items[:2]:  # Max 2 discovery items per category
                        if len(variety_recommendations) >= variety_count:
                            break

                        item_info = self.catalogue_lookup[item]

                        # Score based on category preference and discovery factor
                        base_score = preference * 0.6  # Discovery items have lower scores

                        discovery_rec = {
                            'sku_code': item_info['sku_codes'][0],
                            'product_name': item,
                            'category': item_info.get('category_name', 'Unknown'),
                            'score': base_score,
                            'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                            'recommendation_type': 'stable_discovery',
                            'strategy': f'discovery_{category}_day_{rotation_day}',
                            'category_preference': preference,
                            'rotation_day': rotation_day
                        }

                        variety_recommendations.append(discovery_rec)
                        existing_items.add(item)

        # Strategy 3: Seasonal/trending items (if still need more)
        if len(variety_recommendations) < variety_count:
            # Use top popular items that aren't already included
            trending_items = [
                item for item in self.popular_items[:50]
                if item not in existing_items and item in self.catalogue_lookup
            ]

            # Rotate through trending items
            items_needed = variety_count - len(variety_recommendations)
            start_idx = rotation_day * 2  # 2 trending items per rotation day
            selected_trending = trending_items[start_idx:start_idx + items_needed]

            for item in selected_trending:
                if len(variety_recommendations) >= variety_count:
                    break

                item_info = self.catalogue_lookup[item]

                # Lower score for trending items
                base_score = 0.4

                trending_rec = {
                    'sku_code': item_info['sku_codes'][0],
                    'product_name': item,
                    'category': item_info.get('category_name', 'Unknown'),
                    'score': base_score,
                    'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                    'recommendation_type': 'stable_trending',
                    'strategy': f'trending_day_{rotation_day}',
                    'rotation_day': rotation_day
                }

                variety_recommendations.append(trending_rec)
                existing_items.add(item)

        return variety_recommendations[:variety_count]

    def apply_stability_boosting(self, recommendations, user_id, current_date):
        """
        🚀 APPLY STABILITY BOOSTING

        Applies final stability adjustments to ensure consistent performance.

        Args:
            recommendations: Combined core + variety recommendations
            user_id: User ID
            current_date: Current evaluation date

        Returns:
            list: Stability-boosted recommendations
        """

        # Sort by score (core items should naturally be higher)
        recommendations.sort(key=lambda x: x['score'], reverse=True)

        # Apply stability boosting to core items
        for rec in recommendations:
            if rec['recommendation_type'].startswith('stable_core'):
                rec['score'] = min(rec['score'] * self.stability_boost_factor, 1.0)
                rec['stability_boosted'] = True

        # Ensure minimum score for all recommendations (prevents extreme low scores)
        for rec in recommendations:
            rec['score'] = max(rec['score'], 0.1)  # Minimum score of 0.1

        return recommendations

    def update_recommendation_history(self, user_id, recommendations, current_date):
        """Update recommendation history for stability tracking"""
        if user_id not in self.recommendation_history:
            self.recommendation_history[user_id] = {}

        self.recommendation_history[user_id][current_date] = [r['product_name'] for r in recommendations]

    def apply_gentle_performance_smoothing(self, recommendations, user_id, current_date):
        """
        🎯 GENTLE PERFORMANCE SMOOTHING

        Applies minimal smoothing to reduce volatility without hurting performance.
        Unlike aggressive deduplication, this maintains recommendation quality.

        Args:
            recommendations: Base recommendations from ensemble
            user_id: User ID
            current_date: Current evaluation date

        Returns:
            list: Gently smoothed recommendations
        """

        if not self.performance_smoothing_enabled:
            return recommendations

        # Apply gentle score smoothing to reduce extreme variations
        smoothed_recs = []

        for rec in recommendations:
            # Gentle score normalization (prevents extreme highs/lows)
            original_score = rec['score']

            # Apply gentle smoothing: bring extreme scores closer to mean
            if original_score > 0.9:  # Very high scores
                smoothed_score = 0.9 + (original_score - 0.9) * 0.5  # Reduce by 50%
            elif original_score < 0.1:  # Very low scores
                smoothed_score = 0.1 + (original_score - 0.1) * 0.5  # Boost slightly
            else:
                smoothed_score = original_score  # Keep normal scores unchanged

            # Apply gentle stability boost to frequently purchased items
            if rec['product_name'] in self.recommendation_history.get(user_id, {}):
                smoothed_score *= self.stability_boost_factor  # Gentle 1.2x boost

            # Create smoothed recommendation
            smoothed_rec = rec.copy()
            smoothed_rec['score'] = min(smoothed_score, 1.0)  # Cap at 1.0
            smoothed_rec['smoothing_applied'] = True

            smoothed_recs.append(smoothed_rec)

        # Sort by smoothed scores
        smoothed_recs.sort(key=lambda x: x['score'], reverse=True)

        return smoothed_recs

    def add_gentle_variety_boost(self, recommendations, user_id, user_profile, similarity_dict, current_date):
        """
        🌟 GENTLE VARIETY BOOST

        Adds minimal variety without aggressive blocking of preferred items.
        This maintains performance while adding gentle diversity.

        Args:
            recommendations: Smoothed recommendations
            user_id: User ID
            user_profile: User profile
            similarity_dict: Item similarity matrix
            current_date: Current evaluation date

        Returns:
            list: Recommendations with gentle variety boost
        """

        # Only apply gentle variety if we have enough recommendations
        if len(recommendations) < 10:
            return recommendations

        enhanced_recs = recommendations.copy()
        existing_items = set(r['product_name'] for r in enhanced_recs)

        # Add 1-2 discovery items from user's preferred categories (very gentle)
        user_categories = list(user_profile.get('category_preferences', {}).keys())[:2]
        discovery_count = 0
        max_discovery = 2  # Very limited discovery

        for category in user_categories:
            if discovery_count >= max_discovery:
                break

            # Find popular items in this category not already recommended
            category_items = [
                item for item in self.popular_items[:30]  # Top 30 popular items
                if self.item_categories.get(item) == category
                and item not in existing_items
                and item in self.catalogue_lookup
            ]

            if category_items:
                discovery_item = category_items[0]  # Take the most popular
                item_info = self.catalogue_lookup[discovery_item]

                # Create discovery recommendation with moderate score
                discovery_rec = {
                    'sku_code': item_info['sku_codes'][0],
                    'product_name': discovery_item,
                    'category': item_info.get('category_name', 'Unknown'),
                    'score': 0.6,  # Moderate score for discovery
                    'predicted_quantity': self.item_avg_quantities.get(discovery_item, 1.0),
                    'recommendation_type': 'gentle_discovery',
                    'strategy': f'gentle_variety_{category}',
                    'variety_boost': True
                }

                # Insert discovery item in middle of list (not at top)
                insert_position = min(len(enhanced_recs) // 2, len(enhanced_recs) - 1)
                enhanced_recs.insert(insert_position, discovery_rec)
                existing_items.add(discovery_item)
                discovery_count += 1

        return enhanced_recs

    def get_pattern_aware_recommendations(self, user_id, current_date, base_recommendations, num_recommendations=10):
        """
        🧠 PATTERN-AWARE RECOMMENDATION ENHANCEMENT

        Enhances recommendations based on user's contextual purchase patterns:
        1. Day-of-week preferences
        2. Category rotation patterns
        3. Purchase quantity patterns
        4. Temporal context

        Args:
            user_id: User ID
            current_date: Current evaluation date
            base_recommendations: Base recommendations to enhance
            num_recommendations: Target number of recommendations

        Returns:
            list: Pattern-enhanced recommendations
        """
        if not self.pattern_aware_enabled or user_id not in self.user_purchase_patterns:
            return base_recommendations[:num_recommendations]

        current_day = current_date.strftime('%A')
        user_patterns = self.user_purchase_patterns[user_id]
        day_preferences = self.user_day_preferences.get(user_id, {})

        # Debug logging for pattern-aware recommendations
        debug_user = user_id.startswith('570fa519') or user_id.startswith('4bcb4707')
        if debug_user:
            print(f"🧠 PATTERN-AWARE: User {user_id[:8]}... on {current_day}")
            if day_preferences:
                print(f"   📅 {current_day} pattern: {day_preferences.get('item_count', 0)} items, top categories: {list(day_preferences.get('top_categories', {}).keys())[:2]}")

        enhanced_recommendations = []

        # 1. Prioritize items based on day-of-week patterns
        if day_preferences and 'top_categories' in day_preferences:
            preferred_categories = list(day_preferences['top_categories'].keys())

            # Boost recommendations from preferred categories for this day
            for rec in base_recommendations:
                item_category = self.item_categories.get(rec['product_name'], 'Unknown')

                if item_category in preferred_categories:
                    # Boost score for day-appropriate categories
                    category_rank = preferred_categories.index(item_category)
                    boost_factor = 1.3 - (category_rank * 0.1)  # 1.3x, 1.2x, 1.1x boost
                    rec['score'] = rec.get('score', 0.5) * boost_factor
                    rec['pattern_boost'] = f"day_preference_{current_day}"

                    if debug_user:
                        print(f"      ⬆️ BOOSTED {rec['product_name']} ({item_category}) by {boost_factor:.1f}x for {current_day}")

        # 2. Apply quantity pattern adjustments
        quantity_pattern = user_patterns.get('quantity_pattern', {})
        avg_items = quantity_pattern.get('avg_items_per_order', 5)

        # Adjust recommendation count based on user's typical shopping pattern
        if avg_items > 8:  # Bulk shopper
            target_count = min(num_recommendations * 1.2, num_recommendations + 3)
            pattern_type = "bulk_shopper"
        elif avg_items < 3:  # Light shopper
            target_count = max(num_recommendations * 0.8, num_recommendations - 2)
            pattern_type = "light_shopper"
        else:  # Regular shopper
            target_count = num_recommendations
            pattern_type = "regular_shopper"

        target_count = int(target_count)

        if debug_user:
            print(f"   🛒 Shopping pattern: {pattern_type} (avg {avg_items:.1f} items) → target: {target_count}")

        # 3. Category rotation awareness
        if user_id in self.user_category_rotation:
            rotation_patterns = self.user_category_rotation[user_id]
            if rotation_patterns:
                # Find categories that haven't appeared recently
                recent_categories = set()
                if len(rotation_patterns) > 0:
                    recent_categories = set(rotation_patterns[-1]) if rotation_patterns[-1] else set()

                # Boost items from categories not in recent rotation
                for rec in base_recommendations:
                    item_category = self.item_categories.get(rec['product_name'], 'Unknown')
                    if item_category not in recent_categories and item_category != 'Unknown':
                        rec['score'] = rec.get('score', 0.5) * 1.15  # 15% boost for rotation variety
                        rec['pattern_boost'] = rec.get('pattern_boost', '') + '_rotation_variety'

                        if debug_user:
                            print(f"      🔄 ROTATION BOOST {rec['product_name']} ({item_category})")

        # 4. Sort by enhanced scores and apply pattern-aware selection
        enhanced_recs = sorted(base_recommendations, key=lambda x: x.get('score', 0), reverse=True)

        # 5. Apply contextual filtering for better variety
        selected_recommendations = []
        category_counts = {}

        for rec in enhanced_recs:
            if len(selected_recommendations) >= target_count:
                break

            item_category = self.item_categories.get(rec['product_name'], 'Unknown')

            # Limit items per category for better variety (max 40% from any single category)
            max_per_category = max(1, target_count * 0.4)

            if category_counts.get(item_category, 0) < max_per_category:
                selected_recommendations.append(rec)
                category_counts[item_category] = category_counts.get(item_category, 0) + 1

                if debug_user and hasattr(rec, 'pattern_boost'):
                    print(f"      ✅ SELECTED {rec['product_name']} (boost: {rec.get('pattern_boost', 'none')})")

        # 6. Fill remaining slots if needed
        if len(selected_recommendations) < target_count:
            remaining_slots = target_count - len(selected_recommendations)
            selected_items = set(r['product_name'] for r in selected_recommendations)

            for rec in enhanced_recs:
                if len(selected_recommendations) >= target_count:
                    break
                if rec['product_name'] not in selected_items:
                    selected_recommendations.append(rec)
                    selected_items.add(rec['product_name'])

        if debug_user:
            final_items = [r['product_name'] for r in selected_recommendations]
            print(f"   🎯 Final pattern-aware recommendations: {final_items[:5]}")

        return selected_recommendations[:target_count]

    def apply_intelligent_deduplication(self, recommendations, user_id, current_date, user_profile, similarity_dict):
        """
        🎯 INTELLIGENT RECOMMENDATION DEDUPLICATION (BALANCED APPROACH)

        Applies smart deduplication logic with safeguards:
        1. Allow daily essentials even if recently recommended
        2. Apply cooldown only to top-scored non-essential items (to maintain diversity)
        3. Replace blocked items with intelligent alternatives
        4. Ensure minimum recommendation count is maintained

        Args:
            recommendations: List of recommendation dictionaries
            user_id: User ID
            current_date: Current evaluation date
            user_profile: User's purchase profile
            similarity_dict: Item similarity matrix

        Returns:
            list: Deduplicated and enhanced recommendations
        """
        if not self.deduplication_enabled:
            # If deduplication is disabled, just update tracking and return original
            for rec in recommendations:
                self.update_recommendation_tracking(user_id, rec['product_name'], current_date)
            return recommendations

        # Debug logging for specific problematic users
        debug_user = user_id.startswith('570fa519') or user_id.startswith('4bcb4707')
        if debug_user:
            print(f"🔍 DEBUG: Deduplication for user {user_id[:8]}... on {current_date.strftime('%Y-%m-%d')}")
            print(f"   📝 Input recommendations: {[r['product_name'] for r in recommendations[:5]]}")

        deduplicated_recs = []
        blocked_items = []

        # Sort recommendations by score to prioritize high-scoring items
        sorted_recs = sorted(recommendations, key=lambda x: x['score'], reverse=True)

        for rec in sorted_recs:
            item_name = rec['product_name']

            # Check if item is a daily essential
            is_essential = self.is_daily_essential(user_id, item_name)

            # Check if item is in cooldown
            in_cooldown = self.is_item_in_cooldown(user_id, item_name, current_date)

            # Debug logging for cooldown checks
            if debug_user and user_id in self.user_last_recommended and item_name in self.user_last_recommended[user_id]:
                last_rec_date = self.user_last_recommended[user_id][item_name]
                days_since = (current_date - last_rec_date).days
                print(f"      🔍 {item_name}: last_rec={last_rec_date.strftime('%m-%d')}, days_since={days_since}, cooldown={in_cooldown}")

            # TRUE PATTERN-AWARE ROTATION LOGIC:
            # 1. Force rotation for ALL items (including essentials) based on user patterns
            # 2. Allow essentials only every 2-3 days (not daily)
            # 3. Use pattern-aware alternatives for blocked items
            # 4. Ensure minimum recommendations through smart alternatives, not bypassing cooldown

            # Count current essentials and enforce rotation even for them
            current_essentials = sum(1 for r in deduplicated_recs if self.is_daily_essential(user_id, r['product_name']))

            # Pattern-aware essential rotation: allow essentials only every 2-3 days
            essential_rotation_days = 3 if len(sorted_recs) <= 5 else 2  # Longer rotation for light shoppers

            # Check if this essential should be rotated out
            essential_should_rotate = False
            if is_essential and in_cooldown:
                days_since = (current_date - self.user_last_recommended[user_id][item_name]).days
                essential_should_rotate = days_since < essential_rotation_days

            # STRICT rotation logic - no "min_recs_needed" bypass
            should_allow = (
                (is_essential and not essential_should_rotate and current_essentials < 1) or  # Allow 1 essential every 2-3 days
                not in_cooldown  # Allow only if not in cooldown - NO BYPASS
            )

            if should_allow:
                # Allow the recommendation
                deduplicated_recs.append(rec)
                # Update tracking
                self.update_recommendation_tracking(user_id, item_name, current_date)

                if debug_user:
                    if is_essential and not essential_should_rotate:
                        reason = f"essential_rotation_ok_{current_essentials}/1"
                    elif not in_cooldown:
                        reason = "no_cooldown"
                    else:
                        reason = "unknown"
                    print(f"      ✅ ALLOWED {item_name} (reason: {reason})")
            else:
                # Block the recommendation and try to find alternatives
                blocked_items.append((item_name, rec))

                if debug_user:
                    print(f"      ❌ BLOCKED {item_name} (in cooldown, not essential, have enough recs)")

        # ENHANCED PATTERN-AWARE ALTERNATIVE GENERATION
        # Generate enough alternatives to meet the target recommendation count
        target_recommendations = len(sorted_recs)
        needed_alternatives = max(0, target_recommendations - len(deduplicated_recs))

        if needed_alternatives > 0 and blocked_items:
            if debug_user:
                print(f"      🔄 Need {needed_alternatives} alternatives (have {len(deduplicated_recs)}, target {target_recommendations})")

            # Generate pattern-aware alternatives
            pattern_aware_alternatives = self.get_pattern_aware_alternatives(
                user_id, blocked_items, user_profile, similarity_dict, current_date, needed_alternatives
            )

            for alt_rec in pattern_aware_alternatives:
                if len(deduplicated_recs) < target_recommendations:
                    # Double-check that alternative is not in cooldown
                    alt_in_cooldown = self.is_item_in_cooldown(user_id, alt_rec['product_name'], current_date)
                    if not alt_in_cooldown:
                        deduplicated_recs.append(alt_rec)
                        # Update tracking for alternative
                        self.update_recommendation_tracking(user_id, alt_rec['product_name'], current_date)

                        if debug_user:
                            print(f"      ➕ ADDED ALTERNATIVE {alt_rec['product_name']} (pattern-aware)")
                    elif debug_user:
                        print(f"      ❌ ALTERNATIVE {alt_rec['product_name']} also in cooldown")

        # If we still don't have enough recommendations, this is intentional -
        # better to have fewer, more diverse recommendations than to repeat items

        if debug_user:
            final_items = [r['product_name'] for r in deduplicated_recs]
            print(f"   🎯 Final recommendations: {final_items[:5]}")
            print(f"   📊 Blocked {len(blocked_items)} items, generated {len(deduplicated_recs)} final recommendations")

        return deduplicated_recs
    
    def calculate_purchase_probability(self, recommendation, user_repurchase_ratio, user_id):
        """
        Calculate purchase probability based on recommendation context
        
        Args:
            recommendation: Dict containing recommendation details
            user_repurchase_ratio: User's repurchase ratio
            user_id: User ID
            
        Returns:
            float: Purchase probability between 0 and 1
        """
        product_name = recommendation['product_name']
        rec_score = recommendation['score']
        rec_type = recommendation['recommendation_type']
        
        # Get item's global popularity (now using configurable product names)
        item_popularity = self.item_purchase_frequency.get(product_name, 0.01)
        
        if rec_type == 'repurchase':
            # Higher base probability for repurchase items since user bought before
            # Base probability starts higher and is boosted by recommendation score
            base_prob = 0.6 + (0.3 * rec_score)  # Range: 0.6 to 0.9
            
            # Adjust by user's repurchase behavior
            purchase_probability = base_prob * user_repurchase_ratio
            
            # Small boost for popular items (items others frequently buy)
            popularity_boost = min(0.1, item_popularity * 2)  # Cap at 0.1
            purchase_probability += popularity_boost
            
        else:  # discovery items
            # Lower base probability for new items user hasn't purchased before
            # Base probability is more dependent on recommendation score
            base_prob = 0.2 + (0.4 * rec_score)  # Range: 0.2 to 0.6
            
            # Boost by item's global popularity (popular items more likely to be purchased)
            popularity_factor = 1 + (item_popularity * 3)  # Popularity has more impact for discovery
            purchase_probability = base_prob * popularity_factor
            
            # Slight adjustment based on user's openness to new items
            # Users with higher repurchase ratios might be less open to new items
            discovery_openness = 2 - user_repurchase_ratio  # Range: 1 to 1.3
            purchase_probability *= discovery_openness
        
        # Ensure probability is between 0 and 1
        purchase_probability = max(0.0, min(1.0, purchase_probability))
        
        return round(purchase_probability, 4)
    
    def filter_recommendations_personalized(self, recommendations_df):
        """
        Filter recommendations DataFrame using personalized thresholds based on user repurchase ratios
        
        Args:
            recommendations_df: DataFrame with recommendations containing 'customer_id', 'purchase_probability', 
                              and 'user_repurchase_ratio' columns
            
        Returns:
            DataFrame: Filtered recommendations that meet personalized thresholds
        """
        if recommendations_df.empty:
            return recommendations_df
        
        # Create a copy to avoid modifying the original
        filtered_df = recommendations_df.copy()
        
        # Add personalized threshold and user type columns
        def get_personalized_info(user_repurchase_ratio):
            if user_repurchase_ratio >= 0.8:
                return 0.6, 'Conservative'
            elif user_repurchase_ratio >= 0.5:
                return 0.45, 'Moderate'
            else:
                return 0.3, 'Exploratory'
        
        # Apply personalized logic
        filtered_df[['personalized_threshold', 'user_type']] = filtered_df['user_repurchase_ratio'].apply(
            lambda x: pd.Series(get_personalized_info(x))
        )
        
        # Filter based on personalized threshold
        filtered_df = filtered_df[filtered_df['purchase_probability'] >= filtered_df['personalized_threshold']]
        
        # Update show_recommendation to True for filtered items
        filtered_df['show_recommendation'] = True
        
        print(f"✅ Personalized filtering applied:")
        print(f"   Original recommendations: {len(recommendations_df):,}")
        print(f"   Filtered recommendations: {len(filtered_df):,}")
        print(f"   Filtering efficiency: {len(filtered_df)/len(recommendations_df)*100:.1f}%")
        
        # Show breakdown by user type
        user_type_counts = filtered_df['user_type'].value_counts()
        print(f"   User type breakdown:")
        for user_type, count in user_type_counts.items():
            avg_threshold = filtered_df[filtered_df['user_type'] == user_type]['personalized_threshold'].iloc[0]
            print(f"     {user_type}: {count:,} recommendations (threshold: {avg_threshold})")
        
        return filtered_df
    
    def get_selection_recommendations(self, recommendations_df, user_id, strategy='tiered'):
        """
        Provide selection guidance based on purchase probabilities
        
        Args:
            recommendations_df: DataFrame with recommendations
            user_id: User ID to filter recommendations
            strategy: Selection strategy ('tiered', 'type_based', 'top_n', 'personalized')
            
        Returns:
            dict: Selection recommendations with explanations
        """
        user_recs = recommendations_df[recommendations_df['customer_id'] == user_id].copy()
        
        if len(user_recs) == 0:
            return {'message': 'No recommendations found for this user'}
        
        user_repurchase_ratio = user_recs['user_repurchase_ratio'].iloc[0]
        
        if strategy == 'tiered':
            high_confidence = user_recs[user_recs['purchase_probability'] >= 0.7]
            medium_confidence = user_recs[(user_recs['purchase_probability'] >= 0.4) & 
                                        (user_recs['purchase_probability'] < 0.7)]
            low_confidence = user_recs[user_recs['purchase_probability'] < 0.4]
            
            return {
                'strategy': 'Tiered Confidence',
                'high_confidence': {
                    'count': len(high_confidence),
                    'items': high_confidence[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),
                    'recommendation': 'Strong buy - high likelihood of purchase'
                },
                'medium_confidence': {
                    'count': len(medium_confidence),
                    'items': medium_confidence[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),
                    'recommendation': 'Consider adding - moderate likelihood'
                },
                'low_confidence': {
                    'count': len(low_confidence),
                    'items': low_confidence[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),
                    'recommendation': 'Explore option - lower likelihood but might discover something new'
                }
            }
        
        elif strategy == 'type_based':
            repurchase_items = user_recs[user_recs['recommendation_type'] == 'repurchase']
            discovery_items = user_recs[user_recs['recommendation_type'] == 'discovery']
            
            # Different thresholds for different types
            strong_repurchase = repurchase_items[repurchase_items['purchase_probability'] >= 0.6]
            good_discovery = discovery_items[discovery_items['purchase_probability'] >= 0.35]
            
            return {
                'strategy': 'Type-Based Selection',
                'repurchase_recommendations': {
                    'count': len(strong_repurchase),
                    'items': strong_repurchase[['product_name', 'purchase_probability']].to_dict('records'),
                    'threshold': 0.6,
                    'explanation': 'Items you\'ve bought before with high repurchase probability'
                },
                'discovery_recommendations': {
                    'count': len(good_discovery),
                    'items': good_discovery[['product_name', 'purchase_probability']].to_dict('records'),
                    'threshold': 0.35,
                    'explanation': 'New items with good discovery potential'
                }
            }
        
        elif strategy == 'top_n':
            # Top 5-7 items with minimum threshold
            min_threshold = 0.3
            qualified_items = user_recs[user_recs['purchase_probability'] >= min_threshold]
            top_items = qualified_items.nlargest(7, 'purchase_probability')
            
            return {
                'strategy': 'Top-N with Minimum Threshold',
                'selected_items': {
                    'count': len(top_items),
                    'items': top_items[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),
                    'explanation': f'Top {len(top_items)} items with probability >= {min_threshold}'
                }
            }
        
        elif strategy == 'personalized':
            # Adjust thresholds based on user behavior
            if user_repurchase_ratio >= 0.8:  # Conservative user
                threshold = 0.6
                user_type = 'Conservative'
            elif user_repurchase_ratio >= 0.5:  # Moderate user
                threshold = 0.45
                user_type = 'Moderate'
            else:  # Exploratory user
                threshold = 0.3
                user_type = 'Exploratory'
            
            selected_items = user_recs[user_recs['purchase_probability'] >= threshold]
            
            return {
                'strategy': 'Personalized Threshold',
                'user_type': user_type,
                'user_repurchase_ratio': user_repurchase_ratio,
                'threshold': threshold,
                'selected_items': {
                    'count': len(selected_items),
                    'items': selected_items[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),
                    'explanation': f'Items selected based on your {user_type.lower()} shopping pattern'
                }
            }
        
        else:
            return {'error': 'Invalid strategy. Use: tiered, type_based, top_n, or personalized'}
        
    def precompute_static_features(self, customer_orders_df, catalogue_df):
        """Pre-compute all static features that don't change during evaluation - now using configurable product names"""
        print(f"🔧 Pre-computing static features using '{self.product_name_column}' column (one-time setup)...")
        
        # Validate that the specified column exists
        if self.product_name_column not in catalogue_df.columns:
            raise ValueError(f"Column '{self.product_name_column}' not found in catalogue. Available columns: {list(catalogue_df.columns)}")
        
        # Create clean catalogue with unique names (remove duplicates based on chosen column)
        clean_catalogue = catalogue_df.drop_duplicates(subset=[self.product_name_column]).copy()
        
        # Create mappings between names and SKU codes
        print(f"📊 Creating {self.product_name_column}-to-SKU mappings...")
        for _, row in catalogue_df.iterrows():
            product_name = row[self.product_name_column]
            sku_code = row['sku_code']
            
            # Map SKU to name
            self.sku_to_name_mapping[sku_code] = product_name
            
            # Map name to list of SKU codes (multiple SKUs can have same name)
            if product_name not in self.name_to_sku_mapping:
                self.name_to_sku_mapping[product_name] = []
            self.name_to_sku_mapping[product_name].append(sku_code)
        
        # Convert customer orders to use product names
        print(f"📊 Converting orders to use {self.product_name_column}...")
        customer_orders_with_names = customer_orders_df.copy()
        customer_orders_with_names['product_name'] = customer_orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        
        # Remove any orders where we couldn't map the SKU to a name
        customer_orders_with_names = customer_orders_with_names.dropna(subset=['product_name'])
        
        # Item categories (using configurable column as keys)
        self.item_categories = dict(zip(clean_catalogue[self.product_name_column], clean_catalogue['category_name']))
        
        # Catalogue lookup (using configurable column as keys)
        self.catalogue_lookup = {}
        for _, row in clean_catalogue.iterrows():
            product_name = row[self.product_name_column]
            self.catalogue_lookup[product_name] = {
                'name': product_name,
                'category_name': row['category_name'],
                'sku_codes': self.name_to_sku_mapping[product_name],  # List of all SKU codes for this product
                'original_name': row.get('name', product_name),  # Keep original name for reference
                'parent_name': row.get('sku_parent_name', product_name)  # Keep parent name for reference
            }
        
        # Global item statistics (computed using product names)
        total_orders = customer_orders_with_names['display_order_id'].nunique()
        
        # Group by product name and aggregate
        name_counts = customer_orders_with_names.groupby('product_name').size()
        self.item_purchase_frequency = (name_counts / total_orders).to_dict()
        
        # Average quantities by product name
        self.item_avg_quantities = customer_orders_with_names.groupby('product_name')['ordered_qty'].mean().to_dict()
        
        # Popular items (products purchased by at least 3 users)
        name_user_counts = customer_orders_with_names.groupby('product_name')['customer_id'].nunique()
        self.popular_items = name_user_counts[name_user_counts >= 3].index.tolist()
        
        print(f"✅ Pre-computed {len(self.item_categories)} product categories using '{self.product_name_column}'")
        print(f"✅ Pre-computed {len(self.popular_items)} popular products")
        print(f"✅ Pre-computed mappings: {len(self.name_to_sku_mapping)} {self.product_name_column} values → {sum(len(skus) for skus in self.name_to_sku_mapping.values())} SKUs")
        print(f"✅ Pre-computed global statistics using {self.product_name_column}")
    
    def build_matrices_ultra_fast(self, orders_df, eval_date=None):
        """Build matrices using ultra-fast vectorized operations - now using configurable product names"""

        # Convert orders to use product names
        orders_with_names = orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])

        # Create data hash for caching - include eval_date to ensure different training sets get different hashes
        eval_date_str = eval_date.strftime('%Y-%m-%d') if eval_date is not None else 'no_eval_date'
        data_hash = hashlib.md5(f"{len(orders_with_names)}_{orders_with_names['delivery_date'].min()}_{orders_with_names['delivery_date'].max()}_{self.product_name_column}_{eval_date_str}".encode()).hexdigest()
        
        if data_hash in self.cache:
            print(f"✅ Using cached matrices for '{self.product_name_column}'")
            return self.cache[data_hash]
        
        print(f"⚡ Building matrices (vectorized using {self.product_name_column})...")
        
        # User-item frequency matrix (using configurable product names)
        user_item_counts = orders_with_names.groupby(['customer_id', 'product_name']).size().reset_index(name='count')
        user_item_matrix = user_item_counts.pivot(index='customer_id', columns='product_name', values='count').fillna(0)
        
        # User-item quantity matrix (using configurable product names)
        user_item_qty = orders_with_names.groupby(['customer_id', 'product_name'])['ordered_qty'].mean().reset_index()
        user_item_qty_matrix = user_item_qty.pivot(index='customer_id', columns='product_name', values='ordered_qty').fillna(0)
        
        # 🚀 AGGRESSIVE SIMILARITY MATRIX for HIGH RECALL
        print(f"⚡ Computing AGGRESSIVE similarity matrix for high recall using {self.product_name_column}...")
        popular_in_matrix = [item for item in self.popular_items if item in user_item_matrix.columns]
        
        similarity_dict = {}
        if len(popular_in_matrix) > 0:
            # Use simplified Jaccard on binary matrix
            user_item_binary = (user_item_matrix[popular_in_matrix] > 0).astype(int)
            
            # MUCH MORE AGGRESSIVE similarity computation for high recall
            for i, item1 in enumerate(popular_in_matrix[:200]):  # Doubled from 100 to 200
                item1_users = user_item_binary[item1]
                similarities = {}
                
                for item2 in popular_in_matrix[:200]:  # Consider more items
                    if item1 != item2:
                        item2_users = user_item_binary[item2]
                        intersection = (item1_users & item2_users).sum()
                        union = (item1_users | item2_users).sum()
                        
                        if union > 0 and intersection >= 1:  # LOWERED from 2 to 1
                            jaccard_sim = intersection / union
                            if jaccard_sim > 0.05:  # LOWERED from 0.1 to 0.05 for more connections
                                similarities[item2] = jaccard_sim
                
                if similarities:
                    # Keep top 25 instead of 10 for more recommendations
                    top_similar = dict(sorted(similarities.items(), key=lambda x: x[1], reverse=True)[:25])
                    similarity_dict[item1] = top_similar
        
        # Cache results
        result = {
            'user_item_matrix': user_item_matrix,
            'user_item_qty_matrix': user_item_qty_matrix,
            'similarity_dict': similarity_dict,
            'popular_items': popular_in_matrix
        }
        self.cache[data_hash] = result
        
        print(f"✅ Built matrices using {self.product_name_column}: {user_item_matrix.shape}, similarity: {len(similarity_dict)} items")
        return result
    
    def build_user_profiles_lightning_fast(self, user_item_matrix, user_item_qty_matrix):
        """Build user profiles with minimal computation - now using configurable product names"""
        
        print(f"⚡ Building user profiles (lightning fast using {self.product_name_column})...")
        user_profiles = {}
        
        for user_id in user_item_matrix.index:
            user_row = user_item_matrix.loc[user_id]
            user_items = user_row[user_row > 0].index.tolist()
            
            if not user_items:
                continue
            
            # Pre-computed data (no loops) - now using configurable product names
            purchase_counts = user_row[user_items].to_dict()
            
            # Simplified quantities
            quantities = {}
            if user_id in user_item_qty_matrix.index:
                qty_row = user_item_qty_matrix.loc[user_id]
                quantities = {item: qty for item, qty in qty_row[user_items].items() if qty > 0}
            
            # Simplified category preferences (using configurable product names)
            categories = defaultdict(int)
            for product_name in user_items:
                if product_name in self.item_categories:
                    categories[self.item_categories[product_name]] += purchase_counts[product_name]
            
            # Normalize
            total = sum(categories.values())
            if total > 0:
                categories = {cat: count/total for cat, count in categories.items()}
            
            user_profiles[user_id] = {
                'items': user_items,  # Now contains configurable product names
                'purchase_counts': purchase_counts,
                'quantities': quantities,
                'category_preferences': dict(categories),
                'purchased_items': user_items  # Now contains configurable product names
            }
        
        print(f"✅ Built {len(user_profiles)} user profiles using {self.product_name_column}")
        return user_profiles
    
    def get_recommendations_ultra_fast(self, user_id, user_profile, similarity_dict, top_n=10, repurchase_ratio=None):
        """Ultra-fast recommendation generation with minimal computation - now using configurable product names"""
        
        # Use user-specific repurchase ratio if not provided
        if repurchase_ratio is None:
            repurchase_ratio = self.get_user_repurchase_ratio(user_id)
        
        purchased_items = set(user_profile['purchased_items'])  # Now contains configurable product names
        repurchase_count = max(1, int(top_n * repurchase_ratio))
        discovery_count = max(1, top_n - repurchase_count)
        
        # REPURCHASE RECOMMENDATIONS (simplified) - using configurable product names
        repurchase_scores = {}
        for product_name in purchased_items:
            count = user_profile['purchase_counts'].get(product_name, 0)
            # Simplified scoring for maximum speed
            repurchase_scores[product_name] = count
        
        sorted_repurchase = sorted(repurchase_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Normalize repurchase scores to 0-1 scale
        repurchase_recs = []
        if sorted_repurchase:
            max_repurchase_score = sorted_repurchase[0][1] if sorted_repurchase else 1
            min_repurchase_score = sorted_repurchase[-1][1] if len(sorted_repurchase) > 1 else 0
            score_range = max_repurchase_score - min_repurchase_score
            
            for product_name, raw_score in sorted_repurchase[:repurchase_count]:
                # Normalize score to 0-1 range
                if score_range > 0:
                    normalized_score = (raw_score - min_repurchase_score) / score_range
                else:
                    normalized_score = 1.0
                
                qty = user_profile['quantities'].get(product_name, self.item_avg_quantities.get(product_name, 1.0))
                item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
                
                # Select the first SKU code for this product name
                sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
                
                repurchase_recs.append({
                    'sku_code': sku_code,
                    'product_name': product_name,
                    'category': item_info['category_name'],
                    'score': round(normalized_score, 4),
                    'predicted_quantity': round(qty, 3),
                    'recommendation_type': 'repurchase'
                })
        
        # DISCOVERY RECOMMENDATIONS (ultra-simplified) - using configurable product names
        discovery_scores = defaultdict(float)
        
        # Content-based (only for items with similarities)
        for product_name in purchased_items:
            if product_name in similarity_dict:
                count = user_profile['purchase_counts'].get(product_name, 0)
                for similar_item, sim in similarity_dict[product_name].items():
                    if similar_item not in purchased_items:
                        discovery_scores[similar_item] += sim * count
        
        # Popular items in user's preferred categories
        for product_name in self.popular_items[:200]:  # Only check top 200 popular items
            if product_name not in purchased_items and product_name in self.item_categories:
                category = self.item_categories[product_name]
                if category in user_profile['category_preferences']:
                    cat_pref = user_profile['category_preferences'][category]
                    popularity = self.item_purchase_frequency.get(product_name, 0)
                    discovery_scores[product_name] += cat_pref * popularity * 100  # Scale factor
        
        sorted_discovery = sorted(discovery_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Normalize discovery scores to 0-1 scale
        discovery_recs = []
        if sorted_discovery:
            max_discovery_score = sorted_discovery[0][1] if sorted_discovery else 1
            min_discovery_score = sorted_discovery[-1][1] if len(sorted_discovery) > 1 else 0
            score_range = max_discovery_score - min_discovery_score
            
            for product_name, raw_score in sorted_discovery[:discovery_count]:
                # Normalize score to 0-1 range
                if score_range > 0:
                    normalized_score = (raw_score - min_discovery_score) / score_range
                else:
                    normalized_score = 1.0
                
                qty = self.item_avg_quantities.get(product_name, 1.0)
                item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
                
                # Select the first SKU code for this product name
                sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
                
                discovery_recs.append({
                    'sku_code': sku_code,
                    'product_name': product_name,
                    'category': item_info['category_name'],
                    'score': round(normalized_score, 4),
                    'predicted_quantity': round(qty, 3),
                    'recommendation_type': 'discovery'
                })
        
        # Combine and return
        all_recs = repurchase_recs + discovery_recs
        all_recs.sort(key=lambda x: x['score'], reverse=True)
        return all_recs[:top_n]
    
    def get_recommendations_high_recall_ensemble(self, user_id, user_profile, similarity_dict, top_n=75, repurchase_ratio=None):
        """
        🚀 RADICAL HIGH-RECALL ENSEMBLE RECOMMENDATION SYSTEM
        
        Multi-strategy approach designed to achieve 80%+ recall:
        1. Comprehensive Repurchase (ALL items user bought)
        2. Aggressive Collaborative Filtering (relaxed thresholds)  
        3. Category Completion Strategy
        4. Popular Items Fallback
        5. Hierarchical Product Matching
        """
        
        if repurchase_ratio is None:
            repurchase_ratio = self.get_user_repurchase_ratio(user_id)
        
        purchased_items = set(user_profile['purchased_items'])  # Now contains configurable product names
        all_recommendations = {}  # Use dict to avoid duplicates
        
        # 🎯 STRATEGY 1: COMPREHENSIVE REPURCHASE (60% of recommendations - MORE AGGRESSIVE)
        repurchase_target = max(10, int(top_n * 0.6))  # Increased from 50% to 60%
        
        # Recommend ALL purchased items (not just top ones)
        repurchase_scores = {}
        for product_name in purchased_items:
            count = user_profile['purchase_counts'].get(product_name, 0)
            # Boost recent purchases and frequent purchases
            repurchase_scores[product_name] = count + 0.5  # Base boost
        
        sorted_repurchase = sorted(repurchase_scores.items(), key=lambda x: x[1], reverse=True)
        
        for i, (product_name, raw_score) in enumerate(sorted_repurchase[:repurchase_target]):
            if product_name in all_recommendations:
                continue
                
            # Higher scores for earlier items
            normalized_score = 0.9 - (i * 0.02)  # Start high, decrease slowly
            normalized_score = max(0.5, normalized_score)  # Minimum 0.5
            
            qty = user_profile['quantities'].get(product_name, self.item_avg_quantities.get(product_name, 1.0))
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
            
            all_recommendations[product_name] = {
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(normalized_score, 4),
                'predicted_quantity': round(qty, 3),
                'recommendation_type': 'repurchase',
                'strategy': 'comprehensive_repurchase'
            }
        
        # 🎯 STRATEGY 2: AGGRESSIVE COLLABORATIVE FILTERING (25% of recommendations)
        collab_target = max(5, int(top_n * 0.25))
        
        discovery_scores = defaultdict(float)
        
        # MUCH more aggressive similarity matching
        for product_name in purchased_items:
            if product_name in similarity_dict:
                count = user_profile['purchase_counts'].get(product_name, 0)
                # Include ALL similar items, not just top ones
                for similar_item, sim in similarity_dict[product_name].items():
                    if similar_item not in purchased_items and similar_item not in all_recommendations:
                        # Lower threshold and higher boost
                        discovery_scores[similar_item] += sim * count * 2  # Double the boost
        
        sorted_collab = sorted(discovery_scores.items(), key=lambda x: x[1], reverse=True)
        
        for i, (product_name, raw_score) in enumerate(sorted_collab[:collab_target]):
            if product_name in all_recommendations:
                continue
                
            normalized_score = 0.8 - (i * 0.03)  # High but decreasing
            normalized_score = max(0.4, normalized_score)
            
            qty = self.item_avg_quantities.get(product_name, 1.0)
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
            
            all_recommendations[product_name] = {
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(normalized_score, 4),
                'predicted_quantity': round(qty, 3),
                'recommendation_type': 'discovery',
                'strategy': 'aggressive_collaborative'
            }
        
        # 🎯 STRATEGY 3: CATEGORY COMPLETION (15% of recommendations)
        category_target = max(3, int(top_n * 0.15))
        
        # For each category user shops in, recommend top items they haven't tried
        category_scores = defaultdict(float)
        for category, preference in user_profile['category_preferences'].items():
            if preference > 0.1:  # User shows interest in this category
                # Find popular items in this category they haven't bought
                for product_name in self.popular_items:
                    if (product_name not in purchased_items and 
                        product_name not in all_recommendations and
                        product_name in self.item_categories and
                        self.item_categories[product_name] == category):
                        
                        popularity = self.item_purchase_frequency.get(product_name, 0)
                        category_scores[product_name] += preference * popularity * 150  # Higher boost
        
        sorted_category = sorted(category_scores.items(), key=lambda x: x[1], reverse=True)
        
        for i, (product_name, raw_score) in enumerate(sorted_category[:category_target]):
            if product_name in all_recommendations:
                continue
                
            normalized_score = 0.7 - (i * 0.04)
            normalized_score = max(0.3, normalized_score)
            
            qty = self.item_avg_quantities.get(product_name, 1.0)
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
            
            all_recommendations[product_name] = {
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(normalized_score, 4),
                'predicted_quantity': round(qty, 3),
                'recommendation_type': 'discovery',
                'strategy': 'category_completion'
            }
        
        # 🎯 STRATEGY 4: POPULAR ITEMS FALLBACK (Remaining slots)
        remaining_slots = top_n - len(all_recommendations)
        if remaining_slots > 0:
            # Add globally popular items as safety net
            popular_scores = {}
            for product_name in self.popular_items[:150]:  # Increased from 100 to 150
                if (product_name not in purchased_items and 
                    product_name not in all_recommendations):
                    popularity = self.item_purchase_frequency.get(product_name, 0)
                    popular_scores[product_name] = popularity
            
            sorted_popular = sorted(popular_scores.items(), key=lambda x: x[1], reverse=True)
            
            for i, (product_name, raw_score) in enumerate(sorted_popular[:remaining_slots]):
                if product_name in all_recommendations:
                    continue
                    
                normalized_score = 0.5 - (i * 0.02)  # More generous scoring
                normalized_score = max(0.15, normalized_score)  # Lower minimum
                
                qty = self.item_avg_quantities.get(product_name, 1.0)
                item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
                sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
                
                all_recommendations[product_name] = {
                    'sku_code': sku_code,
                    'product_name': product_name,
                    'category': item_info['category_name'],
                    'score': round(normalized_score, 4),
                    'predicted_quantity': round(qty, 3),
                    'recommendation_type': 'discovery',
                    'strategy': 'popular_fallback'
                }
        
        # Convert to list and sort by score
        final_recommendations = list(all_recommendations.values())
        final_recommendations.sort(key=lambda x: x['score'], reverse=True)
        
        return final_recommendations[:top_n]

    def apply_intelligent_diversity_heuristic(self, recommendations, user_id, user_profile, current_date, similarity_dict):
        """
        🌟 INTELLIGENT DIVERSITY HEURISTIC

        Adds diversity to recommendations while maintaining relevance by:
        1. Identifying daily/frequent items that should appear regularly
        2. Rotating non-essential items to prevent monotony
        3. Introducing category-based variety while preserving precision/recall

        Args:
            recommendations: Base recommendations from ensemble
            user_id: User ID
            user_profile: User profile with purchase history
            current_date: Current evaluation date
            similarity_dict: Item similarity matrix

        Returns:
            list: Diversified recommendations maintaining relevance
        """

        if len(recommendations) == 0:
            return recommendations

        # Step 1: Analyze user's purchase frequency patterns
        purchased_items = user_profile.get('purchased_items', [])
        purchase_counts = user_profile.get('purchase_counts', {})
        total_purchases = len(purchased_items)

        # Calculate frequency scores for each item
        item_frequencies = {}
        for item in set(purchased_items):
            frequency = purchase_counts.get(item, 0) / total_purchases if total_purchases > 0 else 0
            item_frequencies[item] = frequency

        # Step 2: Classify items by purchase frequency (using configurable thresholds)
        # Daily essentials: items purchased frequently (>= daily_essential_threshold)
        daily_essentials = {item: freq for item, freq in item_frequencies.items() if freq >= self.daily_essential_threshold}

        # Weekly regulars: items purchased moderately (weekly_regular_threshold to daily_essential_threshold)
        weekly_regulars = {item: freq for item, freq in item_frequencies.items() if self.weekly_regular_threshold <= freq < self.daily_essential_threshold}

        # Occasional items: items purchased rarely (< weekly_regular_threshold)
        occasional_items = {item: freq for item, freq in item_frequencies.items() if freq < self.weekly_regular_threshold}

        # Debug logging for sample users
        debug_user = user_id.startswith('570fa519') or user_id.startswith('4bcb4707')
        if debug_user:
            print(f"🌟 ENHANCED DIVERSITY HEURISTIC for user {user_id[:8]}... on {current_date.strftime('%A')}:")
            print(f"   📊 Daily essentials: {len(daily_essentials)} items (≥{self.daily_essential_threshold:.0%})")
            print(f"   📅 Weekly regulars: {len(weekly_regulars)} items ({self.weekly_regular_threshold:.0%}-{self.daily_essential_threshold:.0%})")
            print(f"   🎲 Occasional items: {len(occasional_items)} items (<{self.weekly_regular_threshold:.0%})")
            print(f"   🔄 Diversity rotation strength: {self.diversity_rotation_strength:.0%}")
            if daily_essentials:
                print(f"   🔥 Top daily essentials: {list(daily_essentials.keys())[:3]}")
            if weekly_regulars:
                print(f"   📋 Sample weekly regulars: {list(weekly_regulars.keys())[:3]}")

        # Step 3: Apply enhanced diversity logic
        diversified_recs = []
        existing_items = set()

        # Calculate how many slots to reserve for diversity rotation
        total_slots = len(recommendations)
        diversity_slots = int(total_slots * self.diversity_rotation_strength)
        essential_slots = total_slots - diversity_slots

        # Always include daily essentials (high frequency items) - but limit to essential_slots
        essential_count = 0
        for rec in recommendations:
            item_name = rec['product_name']
            if item_name in daily_essentials and essential_count < essential_slots:
                diversified_recs.append(rec)
                existing_items.add(item_name)
                essential_count += 1

        # Step 4: Add weekly regulars with enhanced rotation (based on day of week)
        day_of_week = current_date.weekday()  # 0=Monday, 6=Sunday
        weekly_items = list(weekly_regulars.keys())

        # Enhanced rotation: rotate weekly items more aggressively
        if weekly_items and len(diversified_recs) < total_slots:
            # Use a 2-day rotation cycle for more variety
            rotation_cycle = 2
            items_per_cycle = max(1, len(weekly_items) // rotation_cycle)
            cycle_day = day_of_week % rotation_cycle
            start_idx = (cycle_day * items_per_cycle) % len(weekly_items)

            # Take more items per cycle for better variety
            items_to_take = min(items_per_cycle + 1, len(weekly_items))
            rotated_weekly = weekly_items[start_idx:start_idx + items_to_take]
            if len(rotated_weekly) < items_to_take and weekly_items:
                rotated_weekly.extend(weekly_items[:items_to_take - len(rotated_weekly)])

            weekly_added = 0
            max_weekly = min(diversity_slots // 2, len(rotated_weekly))  # Use half of diversity slots for weekly items

            for rec in recommendations:
                if (rec['product_name'] in rotated_weekly and
                    rec['product_name'] not in existing_items and
                    weekly_added < max_weekly):
                    diversified_recs.append(rec)
                    existing_items.add(rec['product_name'])
                    weekly_added += 1

        # Step 5: Add enhanced variety from different categories and occasional items
        user_categories = user_profile.get('category_preferences', {})
        category_rotation_day = day_of_week % 3  # 3-day category rotation

        # Get categories sorted by preference
        sorted_categories = sorted(user_categories.items(), key=lambda x: x[1], reverse=True)

        # Select categories for today based on rotation - be more aggressive
        selected_categories = []
        for i, (category, preference) in enumerate(sorted_categories[:6]):  # Consider top 6 categories
            if i % 3 == category_rotation_day and preference > 0.03:  # Lower threshold for more variety
                selected_categories.append(category)

        # Also add some categories from different rotation days for more variety
        if len(selected_categories) < 2:
            for i, (category, preference) in enumerate(sorted_categories[:9]):
                if len(selected_categories) >= 3:
                    break
                if category not in selected_categories and preference > 0.02:
                    selected_categories.append(category)

        # Add items from selected categories and occasional items
        variety_added = 0
        remaining_diversity_slots = diversity_slots - weekly_added

        for rec in recommendations:
            if len(diversified_recs) >= total_slots or variety_added >= remaining_diversity_slots:
                break

            item_name = rec['product_name']
            item_category = self.item_categories.get(item_name, 'Unknown')

            # Prioritize occasional items and items from selected categories
            should_add = (item_name not in existing_items and
                         (item_category in selected_categories or
                          item_name in occasional_items or
                          (item_category != 'Unknown' and variety_added < remaining_diversity_slots // 2)))

            if should_add:
                diversified_recs.append(rec)
                existing_items.add(item_name)
                variety_added += 1

        # Step 6: Fill remaining slots with original recommendations (maintain performance)
        for rec in recommendations:
            if len(diversified_recs) >= len(recommendations):
                break

            if rec['product_name'] not in existing_items:
                diversified_recs.append(rec)
                existing_items.add(rec['product_name'])

        # Step 7: Add diversity boost metadata
        for rec in diversified_recs:
            item_name = rec['product_name']
            if item_name in daily_essentials:
                rec['diversity_type'] = 'daily_essential'
                rec['frequency_score'] = daily_essentials[item_name]
            elif item_name in weekly_regulars:
                rec['diversity_type'] = 'weekly_regular'
                rec['frequency_score'] = weekly_regulars[item_name]
            else:
                rec['diversity_type'] = 'variety_item'
                rec['frequency_score'] = occasional_items.get(item_name, 0.0)

        # Debug logging for results
        if debug_user:
            diversity_counts = {}
            for rec in diversified_recs:
                div_type = rec.get('diversity_type', 'unknown')
                diversity_counts[div_type] = diversity_counts.get(div_type, 0) + 1
            print(f"   ✅ Enhanced diversified recommendations: {diversity_counts}")
            print(f"   📈 Total recommendations: {len(diversified_recs)} (from {len(recommendations)} original)")
            print(f"   🎯 Diversity slots used: {len(diversified_recs) - essential_count}/{diversity_slots}")

            # Show actual recommended items for comparison
            rec_items = [rec['product_name'] for rec in diversified_recs[:5]]
            print(f"   📋 Top 5 recommendations: {rec_items}")

        return diversified_recs

    def apply_performance_preserving_diversity(self, base_recommendations, user_id, user_profile, current_date, top_n, repurchase_ratio=None):
        """
        🎯 PERFORMANCE-PRESERVING DIVERSITY SYSTEM

        Smart diversity that maintains high precision/recall:
        1. Preserve top high-scoring items (core performance)
        2. Apply date-aware rotation to lower-scoring items
        3. Maintain relevance while adding variety
        4. Target 40%+ precision/recall with diversity

        Args:
            base_recommendations: High-quality recommendations from ensemble
            user_id: User ID
            user_profile: User profile
            current_date: Current date
            top_n: Final number of recommendations needed
            repurchase_ratio: User's repurchase ratio (optional)

        Returns:
            list: Performance-preserving diverse recommendations
        """

        if len(base_recommendations) == 0:
            return base_recommendations

        # Step 1: Analyze user's purchase patterns for smart diversity
        purchased_items = user_profile.get('purchased_items', [])
        purchase_counts = user_profile.get('purchase_counts', {})
        total_purchases = len(purchased_items)

        # Calculate frequency scores
        item_frequencies = {}
        for item in set(purchased_items):
            frequency = purchase_counts.get(item, 0) / total_purchases if total_purchases > 0 else 0
            item_frequencies[item] = frequency

        # Classify items by purchase frequency
        daily_essentials = {item: freq for item, freq in item_frequencies.items() if freq >= self.daily_essential_threshold}
        weekly_regulars = {item: freq for item, freq in item_frequencies.items() if self.weekly_regular_threshold <= freq < self.daily_essential_threshold}

        # Step 2: K-AWARE ADAPTIVE PERFORMANCE RATIOS
        if repurchase_ratio is None:
            repurchase_ratio = self.get_user_repurchase_ratio(user_id)

        # Base ratios based on user repurchase behavior
        if repurchase_ratio >= self.high_repurchase_threshold:
            base_core_ratio = self.high_repurchase_core_ratio  # 70% core for high-repurchase users
        else:
            base_core_ratio = self.core_performance_ratio  # 50% core for regular users

        # Apply k-aware diversity scaling
        if self.k_diversity_scaling and top_n > self.k_scaling_threshold:
            # Scale diversity based on k value: larger k = more diversity
            k_factor = min(1.0, (top_n - self.k_scaling_threshold) / 10.0)  # Scale factor 0-1
            diversity_boost = k_factor * (self.max_diversity_ratio - self.min_diversity_ratio)
            target_diversity_ratio = self.min_diversity_ratio + diversity_boost

            # Adjust core ratio to accommodate increased diversity
            core_ratio = max(0.3, 1.0 - target_diversity_ratio)  # Minimum 30% core items
        else:
            core_ratio = base_core_ratio

        core_count = max(1, int(top_n * core_ratio))
        diversity_count = top_n - core_count

        # Smart core selection: prioritize high-likelihood purchases
        core_recommendations = []
        diversity_candidates = []

        # Separate high-likelihood items (purchased items) from discovery items
        purchased_item_names = set(purchased_items)

        high_likelihood_recs = []
        discovery_recs = []

        for rec in base_recommendations:
            if rec['product_name'] in purchased_item_names:
                high_likelihood_recs.append(rec)
            else:
                discovery_recs.append(rec)

        # Fill core with high-likelihood items first (these drive performance)
        core_from_purchased = min(core_count, len(high_likelihood_recs))
        for i in range(core_from_purchased):
            rec = high_likelihood_recs[i]
            rec['diversity_type'] = 'core_performance_purchased'
            core_recommendations.append(rec)

        # Fill remaining core slots with top discovery items
        remaining_core_slots = core_count - len(core_recommendations)
        for i in range(min(remaining_core_slots, len(discovery_recs))):
            rec = discovery_recs[i]
            rec['diversity_type'] = 'core_performance_discovery'
            core_recommendations.append(rec)

        # Remaining items become diversity candidates
        diversity_candidates = high_likelihood_recs[core_from_purchased:] + discovery_recs[remaining_core_slots:]

        # Step 3: Apply smart diversity to remaining slots
        diversified_items = []
        used_items = set(rec['product_name'] for rec in core_recommendations)

        # Date-based selection for diversity
        date_seed = int(current_date.strftime('%Y%m%d'))
        day_of_week = current_date.weekday()

        # Prioritize items based on purchase patterns and date rotation
        for rec in diversity_candidates:
            if len(diversified_items) >= diversity_count:
                break

            item_name = rec['product_name']
            if item_name in used_items:
                continue

            # Smart selection based on purchase patterns and date
            should_include = False

            # Always include daily essentials if they appear in candidates
            if item_name in daily_essentials:
                should_include = True
                rec['diversity_type'] = 'daily_essential'

            # Include weekly regulars with k-aware enhanced rotation
            elif item_name in weekly_regulars:
                # K-aware rotation: more aggressive for higher k values
                rotation_cycle = max(2, 5 - (top_n // 3))  # Smaller cycle for larger k
                item_hash = hash(item_name + str(date_seed)) % rotation_cycle
                if item_hash == day_of_week % rotation_cycle:
                    should_include = True
                    rec['diversity_type'] = 'weekly_regular'

            # Include variety items with k-aware enhanced probability
            else:
                item_hash = hash(item_name + str(date_seed)) % 100
                # K-aware selection: higher probability for larger k values
                base_probability = min(80, 50 + (top_n * 2))  # Scale with k
                score_boost = min(20, int(rec['score'] * 40))  # Score-based boost
                selection_probability = base_probability + score_boost
                if item_hash < selection_probability:
                    should_include = True
                    rec['diversity_type'] = 'date_variety'

            if should_include:
                diversified_items.append(rec)
                used_items.add(item_name)

        # Step 4: Fill remaining slots with best available items (maintain performance)
        remaining_slots = diversity_count - len(diversified_items)
        if remaining_slots > 0:
            for rec in diversity_candidates:
                if remaining_slots <= 0:
                    break
                if rec['product_name'] not in used_items:
                    rec['diversity_type'] = 'performance_filler'
                    diversified_items.append(rec)
                    used_items.add(rec['product_name'])
                    remaining_slots -= 1

        # Step 5: Combine core and diversified recommendations
        final_recommendations = core_recommendations + diversified_items

        # Debug logging with k-aware adaptive ratio information
        debug_user = user_id.startswith('570fa519') or user_id.startswith('4bcb4707')
        if debug_user:
            user_type = "HIGH-REPURCHASE" if repurchase_ratio >= self.high_repurchase_threshold else "REGULAR"
            k_scaling_info = f"(k={top_n}, scaling={'ON' if self.k_diversity_scaling and top_n > self.k_scaling_threshold else 'OFF'})"
            print(f"🎯 K-AWARE PERFORMANCE-PRESERVING DIVERSITY for user {user_id[:8]}... on {current_date.strftime('%A')}:")
            print(f"   👤 User type: {user_type} (repurchase ratio: {repurchase_ratio:.3f})")
            print(f"   📏 K-aware scaling: {k_scaling_info}")
            print(f"   ⚖️  Core ratio: {core_ratio:.0%}, Diversity ratio: {1-core_ratio:.0%}")
            print(f"   🔥 Core performance items: {len(core_recommendations)}")
            print(f"   🎲 Diversified items: {len(diversified_items)}")
            print(f"   📊 Total recommendations: {len(final_recommendations)}")

            # Show diversity breakdown
            diversity_counts = {}
            for rec in final_recommendations:
                div_type = rec.get('diversity_type', 'unknown')
                diversity_counts[div_type] = diversity_counts.get(div_type, 0) + 1
            print(f"   📈 Diversity breakdown: {diversity_counts}")

            # Show top items
            top_items = [rec['product_name'] for rec in final_recommendations[:5]]
            print(f"   📋 Top 5 items: {top_items}")

        return final_recommendations

    def blend_with_stable_variety_recommendations(self, base_recommendations, user_id, user_profile, similarity_dict, current_date, top_n, repurchase_ratio=None):
        """
        🌟 BLEND WITH STABLE VARIETY RECOMMENDATIONS

        Combines our performance-preserving diversity with stable variety strategies:
        1. Keep high-performance core items
        2. Replace some diversity items with stable variety strategies
        3. Use variety rotation, discovery, and trending strategies
        4. Maintain overall performance while enhancing variety

        Args:
            base_recommendations: Recommendations from performance-preserving diversity
            user_id: User ID
            user_profile: User profile
            similarity_dict: Item similarity matrix
            current_date: Current date
            top_n: Target number of recommendations
            repurchase_ratio: User's repurchase ratio

        Returns:
            list: Blended recommendations with stable variety strategies
        """

        if len(base_recommendations) == 0:
            return base_recommendations

        # Step 1: Separate core performance items from diversity items
        core_items = []
        diversity_items = []

        for rec in base_recommendations:
            if rec.get('diversity_type', '').startswith('core_performance'):
                core_items.append(rec)
            else:
                diversity_items.append(rec)

        # Step 2: K-aware allocation of slots to stable variety strategies
        core_count = len(core_items)
        remaining_slots = top_n - core_count

        # K-aware allocation: more stable variety for higher k values
        if top_n >= 8:  # For k=8+, use more stable variety strategies
            stable_variety_ratio = 0.8  # 80% stable variety for high k
        elif top_n >= 5:  # For k=5-7, use moderate stable variety
            stable_variety_ratio = 0.6  # 60% stable variety for medium k
        else:  # For k<5, use less stable variety
            stable_variety_ratio = 0.4  # 40% stable variety for low k

        stable_variety_slots = int(remaining_slots * stable_variety_ratio)
        our_diversity_slots = remaining_slots - stable_variety_slots

        # Step 3: Keep best items from our diversity system
        selected_diversity = diversity_items[:our_diversity_slots]

        # Step 4: Create stable profile for variety generation
        stable_profile = self.create_stable_profile_for_variety(user_profile)

        # Step 5: Generate stable variety recommendations
        if stable_variety_slots > 0:
            stable_variety_recs = self.generate_stable_variety_recommendations(
                user_id, stable_profile, similarity_dict, current_date,
                stable_variety_slots, core_items + selected_diversity
            )
        else:
            stable_variety_recs = []

        # Step 6: Combine all recommendations
        blended_recommendations = core_items + selected_diversity + stable_variety_recs

        # Step 7: Sort by score to maintain performance
        blended_recommendations.sort(key=lambda x: x['score'], reverse=True)

        # Debug logging with k-aware information
        debug_user = user_id.startswith('570fa519') or user_id.startswith('4bcb4707')
        if debug_user:
            print(f"🌟 K-AWARE BLENDED RECOMMENDATIONS for user {user_id[:8]}... on {current_date.strftime('%A')}:")
            print(f"   📏 k={top_n}, Stable variety ratio: {stable_variety_ratio:.0%}")
            print(f"   🔥 Core items: {len(core_items)}")
            print(f"   🎯 Our diversity: {len(selected_diversity)}")
            print(f"   🌟 Stable variety: {len(stable_variety_recs)}")
            print(f"   📊 Total blended: {len(blended_recommendations)}")

            # Show strategy breakdown
            strategy_counts = {}
            for rec in blended_recommendations:
                strategy = rec.get('strategy', rec.get('diversity_type', 'unknown'))
                strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
            print(f"   📈 Strategy breakdown: {strategy_counts}")

            # Show top items
            top_items = [rec['product_name'] for rec in blended_recommendations[:5]]
            print(f"   📋 Top 5 blended items: {top_items}")

        return blended_recommendations[:top_n]

    def enforce_daily_rotation(self, recommendations, user_id, current_date, top_n):
        """
        🔄 ENFORCE DAILY ROTATION TO PREVENT IDENTICAL RECOMMENDATIONS

        Ensures that a minimum percentage of items change from day to day
        while maintaining relevance and performance.

        Args:
            recommendations: Current day's recommendations
            user_id: User ID
            current_date: Current date
            top_n: Number of recommendations

        Returns:
            list: Rotation-enforced recommendations with guaranteed daily variety
        """

        if not self.daily_rotation_enforcement:
            return recommendations

        # Initialize user's rotation history if not exists
        if user_id not in self.daily_rotation_history:
            self.daily_rotation_history[user_id] = {}

        user_history = self.daily_rotation_history[user_id]
        current_date_str = current_date.strftime('%Y-%m-%d')

        # Get recent recommendations (last few days)
        recent_dates = []
        for i in range(1, self.rotation_memory_days + 1):
            past_date = current_date - timedelta(days=i)
            past_date_str = past_date.strftime('%Y-%m-%d')
            if past_date_str in user_history:
                recent_dates.append(past_date_str)

        if not recent_dates:
            # No history, store current recommendations and return
            user_history[current_date_str] = [rec['product_name'] for rec in recommendations]
            return recommendations

        # Get yesterday's recommendations for comparison
        yesterday = current_date - timedelta(days=1)
        yesterday_str = yesterday.strftime('%Y-%m-%d')
        yesterday_items = set(user_history.get(yesterday_str, []))

        if not yesterday_items:
            # No yesterday data, store and return
            user_history[current_date_str] = [rec['product_name'] for rec in recommendations]
            return recommendations

        # Calculate required change based on k value
        if top_n <= 3:
            min_change_count = max(1, int(top_n * self.min_daily_change_ratio))  # At least 1 item
        else:
            min_change_count = max(2, int(top_n * self.min_daily_change_ratio))  # At least 2 items

        max_change_count = int(top_n * self.max_daily_change_ratio)

        current_items = set(rec['product_name'] for rec in recommendations)
        overlap_count = len(current_items.intersection(yesterday_items))
        change_needed = max(0, min_change_count - (top_n - overlap_count))

        if change_needed == 0:
            # Sufficient change already, store and return
            user_history[current_date_str] = [rec['product_name'] for rec in recommendations]
            return recommendations

        # Need to force more changes - separate core and diversity items
        core_items = []
        diversity_items = []

        for rec in recommendations:
            if rec.get('diversity_type', '').startswith('core_performance'):
                core_items.append(rec)
            else:
                diversity_items.append(rec)

        # Identify items that need to be rotated out (from yesterday)
        items_to_rotate_out = []
        for rec in diversity_items:  # Only rotate diversity items, keep core items
            if rec['product_name'] in yesterday_items:
                items_to_rotate_out.append(rec)

        # Sort by score (rotate out lowest scoring items first)
        items_to_rotate_out.sort(key=lambda x: x['score'])

        # Remove items that need to be rotated out
        items_to_remove = items_to_rotate_out[:min(change_needed, len(items_to_rotate_out))]
        items_to_keep = [rec for rec in recommendations if rec not in items_to_remove]

        # Generate replacement items from user profile
        replacement_items = self.generate_rotation_replacement_items(
            user_id, items_to_keep, len(items_to_remove), current_date, recent_dates
        )

        # Combine kept items with replacements
        final_recommendations = items_to_keep + replacement_items

        # Sort by score to maintain quality
        final_recommendations.sort(key=lambda x: x['score'], reverse=True)
        final_recommendations = final_recommendations[:top_n]

        # Store in history
        user_history[current_date_str] = [rec['product_name'] for rec in final_recommendations]

        # Clean old history (keep only recent days)
        dates_to_keep = [current_date_str] + recent_dates[:self.rotation_memory_days-1]
        user_history = {date: items for date, items in user_history.items() if date in dates_to_keep}
        self.daily_rotation_history[user_id] = user_history

        # Debug logging for rotation enforcement
        debug_user = user_id.startswith('570fa519') or user_id.startswith('4bcb4707')
        if debug_user and len(items_to_remove) > 0:
            print(f"🔄 DAILY ROTATION ENFORCEMENT for user {user_id[:8]}... on {current_date.strftime('%A')}:")
            print(f"   📊 k={top_n}, Min change needed: {min_change_count}")
            print(f"   ❌ Items rotated out: {[item['product_name'] for item in items_to_remove]}")
            print(f"   ✅ Replacement items: {[item['product_name'] for item in replacement_items]}")
            print(f"   🔄 Final items: {[rec['product_name'] for rec in final_recommendations[:5]]}")

        return final_recommendations

    def generate_rotation_replacement_items(self, user_id, existing_recommendations, num_needed, current_date, recent_dates):
        """
        Generate replacement items for daily rotation that haven't been recommended recently
        """
        if num_needed <= 0:
            return []

        existing_items = set(rec['product_name'] for rec in existing_recommendations)

        # Get items recommended in recent days to avoid
        recent_items = set()
        user_history = self.daily_rotation_history.get(user_id, {})
        for date_str in recent_dates:
            recent_items.update(user_history.get(date_str, []))

        # Get user profile for generating relevant alternatives
        # Note: user_profiles might not be available during evaluation, so we'll use a fallback
        user_profile = getattr(self, 'user_profiles', {}).get(user_id, {})
        purchased_items = user_profile.get('purchased_items', [])
        purchase_counts = user_profile.get('purchase_counts', {})
        total_purchases = len(purchased_items)

        # Generate candidate items from user's purchase history
        candidate_items = []
        for item, count in purchase_counts.items():
            if (item not in existing_items and
                item not in recent_items and
                item in self.catalogue_lookup):

                frequency = count / total_purchases if total_purchases > 0 else 0
                item_info = self.catalogue_lookup[item]

                candidate_rec = {
                    'sku_code': item_info['sku_codes'][0],
                    'product_name': item,
                    'category': item_info.get('category_name', 'Unknown'),
                    'score': frequency * 0.7,  # Slightly lower score for rotation items
                    'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                    'recommendation_type': 'rotation_replacement',
                    'strategy': 'daily_rotation_enforcement',
                    'frequency': frequency
                }
                candidate_items.append(candidate_rec)

        # Sort by score and take top items
        candidate_items.sort(key=lambda x: x['score'], reverse=True)

        # If not enough candidates from purchase history, add popular items
        if len(candidate_items) < num_needed:
            for item in self.popular_items:
                if (item not in existing_items and
                    item not in recent_items and
                    item in self.catalogue_lookup and
                    len(candidate_items) < num_needed * 2):  # Get more candidates

                    item_info = self.catalogue_lookup[item]

                    popular_rec = {
                        'sku_code': item_info['sku_codes'][0],
                        'product_name': item,
                        'category': item_info.get('category_name', 'Unknown'),
                        'score': 0.4,  # Lower score for popular items
                        'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                        'recommendation_type': 'rotation_popular',
                        'strategy': 'daily_rotation_popular',
                    }
                    candidate_items.append(popular_rec)

        return candidate_items[:num_needed]

    def create_stable_profile_for_variety(self, user_profile):
        """
        Create a stable profile compatible with generate_stable_variety_recommendations
        """
        purchased_items = user_profile.get('purchased_items', [])
        purchase_counts = user_profile.get('purchase_counts', {})
        total_purchases = len(purchased_items)

        # Create variety items (items with moderate frequency)
        variety_items = {}
        for item, count in purchase_counts.items():
            frequency = count / total_purchases if total_purchases > 0 else 0
            if 0.05 <= frequency < 0.25:  # Variety items: 5-25% frequency
                variety_items[item] = frequency

        # Create stable categories from user preferences
        stable_categories = user_profile.get('category_preferences', {})

        return {
            'variety_items': variety_items,
            'stable_categories': stable_categories
        }

    def get_date_aware_recommendations(self, user_id, user_profile, similarity_dict, current_date, top_n=75, repurchase_ratio=None):
        """
        🗓️ DATE-AWARE RECOMMENDATION GENERATION

        Generates different recommendations for different dates by:
        1. Creating date-based variation in the core recommendation process
        2. Implementing temporal rotation of items based on purchase patterns
        3. Ensuring daily essentials appear when needed, others rotate

        Args:
            user_id: User ID
            user_profile: User's purchase profile
            similarity_dict: Item similarity matrix
            current_date: Current evaluation date
            top_n: Number of recommendations to generate
            repurchase_ratio: User's repurchase ratio

        Returns:
            list: Date-aware recommendations with built-in diversity
        """

        # Create date-based seed for consistent but varying recommendations
        date_seed = int(current_date.strftime('%Y%m%d'))

        # Analyze user's purchase patterns for this date-aware generation
        purchased_items = user_profile.get('purchased_items', [])
        purchase_counts = user_profile.get('purchase_counts', {})
        total_purchases = len(purchased_items)

        # Calculate frequency scores for each item
        item_frequencies = {}
        for item in set(purchased_items):
            frequency = purchase_counts.get(item, 0) / total_purchases if total_purchases > 0 else 0
            item_frequencies[item] = frequency

        # Classify items by purchase frequency
        daily_essentials = {item: freq for item, freq in item_frequencies.items() if freq >= self.daily_essential_threshold}
        weekly_regulars = {item: freq for item, freq in item_frequencies.items() if self.weekly_regular_threshold <= freq < self.daily_essential_threshold}
        occasional_items = {item: freq for item, freq in item_frequencies.items() if freq < self.weekly_regular_threshold}

        # Calculate how many of each type to recommend
        essential_count = min(len(daily_essentials), max(1, int(top_n * 0.4)))  # 40% for essentials
        regular_count = min(len(weekly_regulars), max(1, int(top_n * 0.4)))     # 40% for regulars
        variety_count = top_n - essential_count - regular_count                  # 20% for variety

        recommendations = []
        used_items = set()

        # Step 1: Add daily essentials (always include these)
        essential_items = sorted(daily_essentials.items(), key=lambda x: x[1], reverse=True)
        for item, freq in essential_items[:essential_count]:
            if item in self.catalogue_lookup:
                item_info = self.catalogue_lookup[item]
                rec = {
                    'sku_code': item_info['sku_codes'][0],
                    'product_name': item,
                    'category': item_info.get('category_name', 'Unknown'),
                    'score': 0.9 + freq * 0.1,  # High score for essentials
                    'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                    'recommendation_type': 'daily_essential',
                    'frequency_score': freq
                }
                recommendations.append(rec)
                used_items.add(item)

        # Step 2: Add weekly regulars with date-based rotation
        if weekly_regulars and regular_count > 0:
            regular_items = list(weekly_regulars.keys())

            # Create date-based rotation
            day_of_week = current_date.weekday()
            rotation_offset = (date_seed + day_of_week) % len(regular_items)

            # Rotate the list based on date
            rotated_regulars = regular_items[rotation_offset:] + regular_items[:rotation_offset]

            added_regulars = 0
            for item in rotated_regulars:
                if added_regulars >= regular_count:
                    break
                if item not in used_items and item in self.catalogue_lookup:
                    item_info = self.catalogue_lookup[item]
                    freq = weekly_regulars[item]
                    rec = {
                        'sku_code': item_info['sku_codes'][0],
                        'product_name': item,
                        'category': item_info.get('category_name', 'Unknown'),
                        'score': 0.7 + freq * 0.2,  # Medium score for regulars
                        'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                        'recommendation_type': 'weekly_regular',
                        'frequency_score': freq,
                        'rotation_day': day_of_week
                    }
                    recommendations.append(rec)
                    used_items.add(item)
                    added_regulars += 1

        # Step 3: Add variety items with date-based selection
        if variety_count > 0:
            # Get popular items not yet used
            available_variety = [
                item for item in self.popular_items[:50]  # Top 50 popular items
                if item not in used_items and item in self.catalogue_lookup
            ]

            # Add category-based variety with date rotation
            user_categories = list(user_profile.get('category_preferences', {}).keys())
            if user_categories:
                category_rotation = (date_seed + current_date.weekday()) % len(user_categories)
                preferred_category = user_categories[category_rotation]

                # Prioritize items from rotated category
                category_items = [
                    item for item in available_variety
                    if self.item_categories.get(item) == preferred_category
                ]

                # Add category items first, then fill with other popular items
                variety_candidates = category_items + [
                    item for item in available_variety if item not in category_items
                ]
            else:
                variety_candidates = available_variety

            # Add variety items with date-based selection
            added_variety = 0
            for i, item in enumerate(variety_candidates):
                if added_variety >= variety_count:
                    break

                # Date-based selection probability
                item_hash = hash(item + str(date_seed)) % 100
                selection_threshold = 70 - (i * 2)  # Decrease threshold for later items

                if item_hash < selection_threshold or added_variety < variety_count // 2:
                    item_info = self.catalogue_lookup[item]
                    rec = {
                        'sku_code': item_info['sku_codes'][0],
                        'product_name': item,
                        'category': item_info.get('category_name', 'Unknown'),
                        'score': 0.5 + (item_hash / 200.0),  # Variable score based on date
                        'predicted_quantity': self.item_avg_quantities.get(item, 1.0),
                        'recommendation_type': 'date_variety',
                        'date_hash': item_hash
                    }
                    recommendations.append(rec)
                    used_items.add(item)
                    added_variety += 1

        # Debug logging
        debug_user = user_id.startswith('570fa519') or user_id.startswith('4bcb4707')
        if debug_user:
            print(f"🗓️ DATE-AWARE RECOMMENDATIONS for user {user_id[:8]}... on {current_date.strftime('%A %Y-%m-%d')}:")
            print(f"   📊 Generated: {len(recommendations)} recommendations")
            print(f"   🔥 Daily essentials: {essential_count}")
            print(f"   📅 Weekly regulars: {added_regulars}")
            print(f"   🎲 Variety items: {len(recommendations) - essential_count - added_regulars}")

            # Show actual items
            rec_items = [rec['product_name'] for rec in recommendations[:5]]
            print(f"   📋 Top 5 items: {rec_items}")

        # Sort by score and return
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        return recommendations[:top_n]

    def get_high_performance_diverse_recommendations(self, user_id, user_profile, similarity_dict, current_date, top_n=75, repurchase_ratio=None):
        """
        🚀 HIGH-PERFORMANCE RECOMMENDATION SYSTEM WITH SMART DIVERSITY

        HYBRID APPROACH FOR OPTIMAL PRECISION/RECALL + DIVERSITY:
        1. Start with proven high-recall ensemble (maintains performance)
        2. Apply intelligent date-aware diversity rotation
        3. Preserve core high-scoring items while rotating variety items
        4. Achieve 40%+ precision/recall while preventing monotony

        Args:
            user_id: User ID
            user_profile: User's purchase profile
            similarity_dict: Item similarity matrix
            current_date: Current evaluation date
            top_n: Number of recommendations to generate
            repurchase_ratio: User's repurchase ratio

        Returns:
            list: High-performance recommendations with smart diversity
        """

        # Step 1: Generate base recommendations using PROVEN high-recall ensemble
        # Get more recommendations to allow for better selection
        base_recommendations = self.get_recommendations_high_recall_ensemble(
            user_id, user_profile, similarity_dict, min(top_n * 3, 150), repurchase_ratio  # Get 3x more for better selection
        )

        # Step 1.5: AGGRESSIVE purchase-likelihood boosting for 40%+ performance
        purchased_items = user_profile.get('purchased_items', [])
        purchase_counts = user_profile.get('purchase_counts', {})
        total_purchases = len(purchased_items)

        # Get recent purchases (last 30 days) for additional boost
        recent_items = set()
        if 'recent_purchases' in user_profile:
            recent_items = set(user_profile['recent_purchases'])

        for rec in base_recommendations:
            item_name = rec['product_name']
            original_score = rec['score']

            # Frequency-based boost (more aggressive)
            if item_name in purchase_counts and total_purchases > 0:
                frequency = purchase_counts[item_name] / total_purchases
                frequency_boost = frequency * 0.5  # Up to 50% boost (was 30%)
                rec['score'] = min(1.0, rec['score'] + frequency_boost)

            # Recent purchase boost (very aggressive for recent items)
            if item_name in recent_items:
                recent_boost = 0.3  # 30% boost for recent purchases
                rec['score'] = min(1.0, rec['score'] + recent_boost)

            # High-frequency item super boost (for items purchased >30% of the time)
            if item_name in purchase_counts and total_purchases > 0:
                frequency = purchase_counts[item_name] / total_purchases
                if frequency >= 0.3:  # Items purchased 30%+ of the time
                    super_boost = 0.2  # Additional 20% boost
                    rec['score'] = min(1.0, rec['score'] + super_boost)

            rec['total_boost'] = rec['score'] - original_score

        # Re-sort by boosted scores
        base_recommendations.sort(key=lambda x: x['score'], reverse=True)

        if not self.diversity_heuristic_enabled:
            # If diversity is disabled, return original ensemble results
            return base_recommendations[:top_n]

        # Step 2: Apply smart diversity while preserving performance
        diversified_recommendations = self.apply_performance_preserving_diversity(
            base_recommendations, user_id, user_profile, current_date, top_n, repurchase_ratio
        )

        # Step 2.5: Blend with stable variety recommendations for enhanced diversity
        blended_recommendations = self.blend_with_stable_variety_recommendations(
            diversified_recommendations, user_id, user_profile, similarity_dict, current_date, top_n, repurchase_ratio
        )

        # Step 2.6: Enforce daily rotation to prevent identical recommendations
        rotation_enforced_recommendations = self.enforce_daily_rotation(
            blended_recommendations, user_id, current_date, top_n
        )

        # Step 3: Apply gentle performance smoothing
        smoothed_recommendations = self.apply_gentle_performance_smoothing(
            rotation_enforced_recommendations, user_id, current_date
        )

        # Step 4: Update tracking for analysis
        self.update_recommendation_history(user_id, smoothed_recommendations, current_date)

        return smoothed_recommendations[:top_n]

    def get_high_performance_stable_recommendations(self, user_id, user_profile, similarity_dict, current_date, top_n=75, repurchase_ratio=None):
        """
        🚀 HIGH-PERFORMANCE STABLE RECOMMENDATION SYSTEM WITH SMART DIVERSITY

        Wrapper method that calls the new high-performance diverse system
        """
        return self.get_high_performance_diverse_recommendations(
            user_id, user_profile, similarity_dict, current_date, top_n, repurchase_ratio
        )


    
    def calculate_personalized_k_values(self, customer_orders_df, lookback_purchases=15):
        """Calculate personalized k values based on median items in user's last N purchases"""
        
        print(f"🎯 Calculating personalized k values based on median items in last {lookback_purchases} purchases...")
        
        # Convert to datetime if not already
        if not pd.api.types.is_datetime64_any_dtype(customer_orders_df['delivery_date']):
            customer_orders_df = customer_orders_df.copy()
            customer_orders_df['delivery_date'] = pd.to_datetime(customer_orders_df['delivery_date'])
        
        personalized_k = {}
        
        # Group by customer and calculate median items per purchase
        for customer_id, customer_orders in customer_orders_df.groupby('customer_id'):
            # Sort by delivery date descending to get most recent purchases first
            customer_orders_sorted = customer_orders.sort_values('delivery_date', ascending=False)
            
            # Get unique orders (display_order_id) and their item counts
            order_item_counts = customer_orders_sorted.groupby('display_order_id').size()
            
            # Take the last N purchases
            recent_purchases = order_item_counts.head(lookback_purchases)
            
            if len(recent_purchases) > 0:
                # Calculate median number of items per purchase
                median_items = recent_purchases.median()
                
                # Set minimum k=3 and maximum k=50 for practical reasons
                personalized_k[customer_id] = max(3, min(50, int(median_items)))
            else:
                # Default fallback if no purchase history
                personalized_k[customer_id] = 10
        
        # Summary statistics
        k_values = list(personalized_k.values())
        print(f"✅ Calculated personalized k for {len(personalized_k):,} users")
        print(f"   Median k across all users: {np.median(k_values):.1f}")
        print(f"   Mean k across all users: {np.mean(k_values):.1f}")
        print(f"   Min k: {min(k_values)}, Max k: {max(k_values)}")
        
        # Distribution of k values
        k_distribution = pd.Series(k_values).value_counts().sort_index()
        print(f"   Most common k values: {k_distribution.head(5).to_dict()}")
        
        return personalized_k

    def evaluate_precision_recall_ultra_fast(self, customer_orders_df, k=None, test_days=7, use_personalized_k=True):
        """🚀 HIGH-RECALL EVALUATION using Multi-Strategy Ensemble with PERSONALIZED k values"""
        
        if use_personalized_k:
            print(f"🚀 HIGH-RECALL PRECISION & RECALL EVALUATION WITH PERSONALIZED k VALUES")
            print(f"🎯 Using median items from last 15 purchases as personalized k for each user")
        else:
            print(f"🚀 HIGH-RECALL PRECISION & RECALL @ k={k} EVALUATION (USING {self.product_name_column.upper()})")
        print(f"🎯 TARGET: 80%+ RECALL with Multi-Strategy Ensemble Approach using '{self.product_name_column}' column")
        print("="*70)
        
        start_total = time.time()
        
        # Convert orders to use product names
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        # Calculate personalized k values if requested
        if use_personalized_k:
            personalized_k_values = self.calculate_personalized_k_values(customer_orders_df)
            default_k = int(np.median(list(personalized_k_values.values())))
            print(f"📊 Using personalized k values (median: {default_k})")
        else:
            personalized_k_values = {}
            default_k = k if k is not None else 10
            print(f"📊 Using fixed k={default_k} for all users")
        
        # Get evaluation dates
        max_date = orders_with_names['delivery_date'].max()
        evaluation_dates = [max_date - timedelta(days=i) for i in range(test_days-1, -1, -1)]
        today_date = pd.Timestamp(datetime.today().date())
        if today_date not in evaluation_dates:
            evaluation_dates.append(datetime.today().date())
        
        all_user_metrics = []
        daily_results = {}
        all_recommendations = []  # Store all recommendations for the new dataframe
        
        # Pre-compute test data for ALL dates at once (batch operation) - using configurable product names
        print(f"📊 Pre-computing test data for all dates using {self.product_name_column}...")
        test_data_all = {}
        for eval_date in evaluation_dates:
            test_data = orders_with_names[orders_with_names['delivery_date'] == eval_date]
            if len(test_data) > 0:
                # Group by customer and create sets of purchased product names
                test_grouped = test_data.groupby('customer_id')['product_name'].apply(set).to_dict()
                test_data_all[eval_date] = test_grouped
        
        for eval_date in evaluation_dates:
            if eval_date not in test_data_all:
                continue
                
            print(f"\n📅 {eval_date.strftime('%Y-%m-%d')}")
            day_start = time.time()
            
            # Training data (using original orders but will convert to names in build_matrices)
            train_data = customer_orders_df[customer_orders_df['delivery_date'] < eval_date]
            
            if len(train_data) == 0:
                continue
            
            print(f"   📊 Train: {len(train_data)} orders | Test: {len(test_data_all[eval_date])} users")
            
            # Build model (with caching) - now uses configurable product names internally
            model_data = self.build_matrices_ultra_fast(train_data, eval_date)
            user_profiles = self.build_user_profiles_lightning_fast(
                model_data['user_item_matrix'], 
                model_data['user_item_qty_matrix']
            )
            
            # 🧠 ANALYZE DAILY PURCHASE PATTERNS for intelligent deduplication
            self.analyze_daily_purchase_patterns(customer_orders_df, eval_date)

            # 🧠 ANALYZE USER PURCHASE PATTERNS for pattern-aware recommendations
            self.analyze_user_purchase_patterns(customer_orders_df, eval_date)

            # 🔍 DEBUG: Show tracking status for debug users
            debug_users = ['570fa519', '4bcb4707']
            for debug_user_prefix in debug_users:
                debug_user = next((uid for uid in user_profiles.keys() if uid.startswith(debug_user_prefix)), None)
                if debug_user and debug_user in self.user_last_recommended:
                    tracked_items = self.user_last_recommended[debug_user]
                    print(f"🔍 DEBUG: User {debug_user[:8]}... tracking: {len(tracked_items)} items")
                    for item, date in list(tracked_items.items())[:3]:
                        days_ago = (eval_date - date).days
                        print(f"      {item}: {days_ago} days ago")

            # Generate recommendations using ENHANCED PATTERN-AWARE SYSTEM with personalized k values
            print(f"   🚀 Generating HIGH-PERFORMANCE STABLE recommendations using proven ensemble with {self.product_name_column}...")
            recommendations = {}
            user_k_values = {}  # Track k values used for each user

            for user_id in user_profiles:
                # Use personalized k value if available, otherwise use default
                user_k = personalized_k_values.get(user_id, default_k)
                user_k_values[user_id] = user_k

                recommendations[user_id] = self.get_high_performance_stable_recommendations(
                    user_id, user_profiles[user_id], model_data['similarity_dict'], eval_date, user_k
                )
            
            # Collect all recommendations for the output dataframe with personalized filtering
            for user_id, user_recs in recommendations.items():
                user_repurchase_ratio = self.get_user_repurchase_ratio(user_id)
                user_k = user_k_values.get(user_id, default_k)
                
                # 🚀 EXTREMELY PERMISSIVE thresholds for 80%+ RECALL (final push!)
                if user_repurchase_ratio >= 0.8:  # Conservative user
                    personalized_threshold = 0.15  # FURTHER LOWERED from 0.25 to 0.15
                    user_type = 'Conservative'
                elif user_repurchase_ratio >= 0.5:  # Moderate user
                    personalized_threshold = 0.10  # FURTHER LOWERED from 0.15 to 0.10
                    user_type = 'Moderate'
                else:  # Exploratory user
                    personalized_threshold = 0.05  # FURTHER LOWERED from 0.10 to 0.05
                    user_type = 'Exploratory'
                
                for rec in user_recs:
                    # Calculate purchase probability based on recommendation context
                    purchase_probability = self.calculate_purchase_probability(
                        rec, user_repurchase_ratio, user_id
                    )
                    
                    # Only include recommendations that meet the personalized threshold
                    if purchase_probability >= personalized_threshold:
                        recommendation_record = {
                            'sku_name': rec['product_name'],  # Keep backward compatibility
                            'product_name': rec['product_name'],  # New column for clarity
                            'sku_code': rec['sku_code'],
                            'show_recommendation': True,  # Set to True for filtered recommendations
                            'recommendation_type': rec['recommendation_type'],
                            'recommendation_score': rec['score'],
                            'purchase_probability': purchase_probability,  # New column with purchase confidence
                            'recommendation_date': eval_date,
                            'predicted_quantity': rec['predicted_quantity'],
                            'customer_id': user_id,
                            'category': rec['category'],
                            'user_repurchase_ratio': user_repurchase_ratio,  # Track the ratio used
                            'personalized_threshold': personalized_threshold,  # Track the threshold used
                            'user_type': user_type,  # Track user classification
                            'product_name_column_used': self.product_name_column,  # Track which column was used
                            'personalized_k': user_k  # Track the k value used for this user
                        }
                        all_recommendations.append(recommendation_record)
            
            # Evaluate (vectorized) - now using configurable product names for comparison
            print(f"   📊 Computing metrics using {self.product_name_column}...")
            actual_purchases = test_data_all[eval_date]
            
            metrics_list = []
            for user_id in recommendations:
                if user_id in actual_purchases:
                    # Get recommended product names (not SKU codes)
                    recommended_items = set([rec['product_name'] for rec in recommendations[user_id]])
                    actual_items = actual_purchases[user_id]  # Already contains configurable product names
                    user_k = user_k_values.get(user_id, default_k)
                    
                    hits = len(recommended_items.intersection(actual_items))
                    precision = hits / len(recommended_items) if len(recommended_items) > 0 else 0
                    recall = hits / len(actual_items) if len(actual_items) > 0 else 0
                    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                    hit_rate = 1 if hits > 0 else 0
                    
                    # Get user repurchase ratio for bucketing
                    user_repurchase_ratio = self.get_user_repurchase_ratio(user_id)
                    
                    # Categorize user based on repurchase ratio
                    if user_repurchase_ratio >= 0.8:
                        user_bucket = 'repurchase'  # High repurchase users
                    elif user_repurchase_ratio >= 0.5:
                        user_bucket = 'balanced'    # Moderate repurchase users
                    else:
                        user_bucket = 'discovery'   # Low repurchase users (more exploratory)
                    
                    metrics_list.append({
                        'customer_id': user_id,  # Changed from user_id to customer_id for consistency
                        'user_id': user_id,      # Keep both for backward compatibility
                        'precision_at_k': precision,
                        'recall_at_k': recall,
                        'f1_at_k': f1,
                        'hit_rate': hit_rate,
                        'hits': hits,
                        'total_recommended': len(recommended_items),
                        'total_actual': len(actual_items),
                        'personalized_k': user_k,
                        'user_repurchase_ratio': user_repurchase_ratio,
                        'user_bucket': user_bucket
                    })
            
            # Daily averages (vectorized)
            if metrics_list:
                k_values_today = [m['personalized_k'] for m in metrics_list]
                daily_avg = {
                    'date': eval_date,
                    'users_evaluated': len(metrics_list),
                    'avg_precision_at_k': np.mean([m['precision_at_k'] for m in metrics_list]),
                    'avg_recall_at_k': np.mean([m['recall_at_k'] for m in metrics_list]),
                    'avg_f1_at_k': np.mean([m['f1_at_k'] for m in metrics_list]),
                    'avg_hit_rate': np.mean([m['hit_rate'] for m in metrics_list]),
                    'median_k': np.median(k_values_today),
                    'mean_k': np.mean(k_values_today),
                    'min_k': np.min(k_values_today),
                    'max_k': np.max(k_values_today),
                    'use_personalized_k': use_personalized_k
                }
                daily_results[eval_date] = daily_avg
                
                # Add to overall metrics
                for m in metrics_list:
                    m.update({'date': eval_date, 'use_personalized_k': use_personalized_k})
                    all_user_metrics.append(m)
                
                print(f"   👥 Users: {len(metrics_list)} | P@k: {daily_avg['avg_precision_at_k']:.3f} | R@k: {daily_avg['avg_recall_at_k']:.3f} | Med k: {daily_avg['median_k']:.0f}")
            
            day_time = time.time() - day_start
            print(f"   ⏱️ Day completed in {day_time:.1f}s")
        
        # Final results
        if all_user_metrics:
            all_k_values = [m['personalized_k'] for m in all_user_metrics]
            overall_metrics = {
                'use_personalized_k': use_personalized_k,
                'median_k': np.median(all_k_values),
                'mean_k': np.mean(all_k_values),
                'min_k': np.min(all_k_values),
                'max_k': np.max(all_k_values),
                'test_days': test_days,
                'total_evaluations': len(all_user_metrics),
                'total_days_evaluated': len(daily_results),
                'overall_avg_precision_at_k': np.mean([m['precision_at_k'] for m in all_user_metrics]),
                'overall_avg_recall_at_k': np.mean([m['recall_at_k'] for m in all_user_metrics]),
                'overall_avg_f1_at_k': np.mean([m['f1_at_k'] for m in all_user_metrics]),
                'overall_avg_hit_rate': np.mean([m['hit_rate'] for m in all_user_metrics]),
                'product_name_column_used': self.product_name_column
            }
        else:
            overall_metrics = {}
        
        total_time = time.time() - start_total
        
        print("\n" + "="*70)
        print(f"🏆 ULTRA-FAST EVALUATION COMPLETED IN {total_time:.1f} SECONDS!")
        print(f"🔍 NOW USING {self.product_name_column.upper()} FOR RECOMMENDATION MATCHING!")
        print("="*70)
        if overall_metrics:
            print(f"📊 Days evaluated: {overall_metrics['total_days_evaluated']}")
            print(f"📊 User evaluations: {overall_metrics['total_evaluations']:,}")
            print(f"📊 Product name column used: {overall_metrics['product_name_column_used']}")
            
            if use_personalized_k:
                print(f"📊 Personalized k values used: median={overall_metrics['median_k']:.0f}, mean={overall_metrics['mean_k']:.1f}, range={overall_metrics['min_k']:.0f}-{overall_metrics['max_k']:.0f}")
                print(f"\n🎯 FINAL HIGH-RECALL RESULTS @ PERSONALIZED k ({self.product_name_column.upper()} MATCHING):")
            else:
                print(f"\n🎯 FINAL HIGH-RECALL RESULTS @ k={default_k} ({self.product_name_column.upper()} MATCHING):")
            
            print(f"   Precision@k: {overall_metrics['overall_avg_precision_at_k']:.4f}")
            print(f"   Recall@k: {overall_metrics['overall_avg_recall_at_k']:.4f}")
            print(f"   F1@k: {overall_metrics['overall_avg_f1_at_k']:.4f}")
            print(f"   Hit Rate: {overall_metrics['overall_avg_hit_rate']:.4f}")
            
            # 🎯 RECALL TARGET CHECK
            if overall_metrics['overall_avg_recall_at_k'] >= 0.8:
                print(f"   🎉 SUCCESS! ACHIEVED 80%+ RECALL TARGET!")
            else:
                print(f"   ⚠️  Still below 80% target. Current: {overall_metrics['overall_avg_recall_at_k']*100:.1f}%")
        print("="*70)
        
        return {
            'overall_metrics': overall_metrics,
            'daily_results': daily_results,
            'all_user_metrics': all_user_metrics,
            'all_recommendations': all_recommendations,
            'total_time_seconds': total_time
        }

# def main():
print("🚀 ULTRA-OPTIMIZED PRECISION & RECALL EVALUATION WITH PERSONALIZED k VALUES")
print("="*70)
print("🎯 Maximum speed optimization with intelligent caching")
print("🔧 NEW: Configurable product name column (name vs sku_parent_name)")
print("🎯 NEW: Personalized k values based on user's last 15 purchase patterns")
print("="*70)

# Pre-compute all static features (one-time cost)
print("\n🔧 SETUP PHASE:")
setup_start = time.time()

# Convert delivery_date to datetime
customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])

# Configure which product name column to use
# Options: 'name' or 'sku_parent_name'
# Based on previous testing: 'sku_parent_name' generally gives higher recall
# PRODUCT_NAME_COLUMN = 'sku_parent_name'  # Change this to 'name' if needed
PRODUCT_NAME_COLUMN = 'name'

print(f"📊 Available columns in catalogue: {list(catalogue_rc.columns)}")
print(f"🔧 Using '{PRODUCT_NAME_COLUMN}' column for product identification")

# Validate that both columns exist (assuming they are always present)
if 'name' not in catalogue_rc.columns or 'sku_parent_name' not in catalogue_rc.columns:
    raise ValueError("Both 'name' and 'sku_parent_name' columns must be present in the catalogue")

# Show comparison stats
unique_names = catalogue_rc['name'].nunique()
unique_parent_names = catalogue_rc['sku_parent_name'].nunique()
print(f"📊 Unique 'name' values: {unique_names:,}")
print(f"📊 Unique 'sku_parent_name' values: {unique_parent_names:,}")

# Initialize system with chosen column
system = HighPerformanceStableRecommendationSystem(
    user_repurchase_ratios_df=repurchase_ratios_df,
    product_name_column=PRODUCT_NAME_COLUMN
)

system.precompute_static_features(customer_orders_rc, catalogue_rc)

# Run evaluation with chosen column and personalized k values
results = system.evaluate_precision_recall_ultra_fast(customer_orders_rc, test_days=7, use_personalized_k=True)

setup_time = time.time() - setup_start
print(f"\n✅ Complete setup and evaluation completed in {setup_time:.2f} seconds")

# Save results
if results['overall_metrics']:
    print("\n💾 Saving results...")
    
    # Convert to DataFrames
    user_metrics_df = pd.DataFrame(results['all_user_metrics'])
    daily_metrics_df = pd.DataFrame([
        {**metrics, 'date': date.strftime('%Y-%m-%d')} 
        for date, metrics in results['daily_results'].items()
    ])
    recommendations_df = pd.DataFrame(results['all_recommendations'])
    
    # Analyze performance by user bucket
    if len(user_metrics_df) > 0 and 'user_bucket' in user_metrics_df.columns:
        print("\n📊 RECALL PERFORMANCE BY USER BUCKET:")
        print("="*60)
        
        bucket_analysis = user_metrics_df.groupby('user_bucket').agg({
            'recall_at_k': ['mean', 'median', 'std', 'count'],
            'precision_at_k': ['mean', 'median'],
            'f1_at_k': ['mean', 'median'],
            'hit_rate': 'mean',
            'personalized_k': 'mean',
            'user_repurchase_ratio': 'mean'
        }).round(4)
        
        # Flatten column names
        bucket_analysis.columns = ['_'.join(col).strip() for col in bucket_analysis.columns]
        
        print("User Bucket Performance Summary:")
        print("-" * 60)
        
        for bucket in ['repurchase', 'balanced', 'discovery']:
            if bucket in bucket_analysis.index:
                row = bucket_analysis.loc[bucket]
                print(f"\n{bucket.upper()} Users (repurchase ratio ≥ {0.8 if bucket=='repurchase' else 0.5 if bucket=='balanced' else 0.0}):")
                print(f"  Count: {int(row['recall_at_k_count'])} users")
                print(f"  Mean Recall@k: {row['recall_at_k_mean']:.4f} (±{row['recall_at_k_std']:.4f})")
                print(f"  Median Recall@k: {row['recall_at_k_median']:.4f}")
                print(f"  Mean Precision@k: {row['precision_at_k_mean']:.4f}")
                print(f"  Hit Rate: {row['hit_rate_mean']:.4f}")
                print(f"  Avg k value: {row['personalized_k_mean']:.1f}")
                print(f"  Avg repurchase ratio: {row['user_repurchase_ratio_mean']:.3f}")
        
        # Show best and worst performing bucket
        bucket_recall_means = bucket_analysis['recall_at_k_mean']
        if len(bucket_recall_means) > 1:
            best_bucket = bucket_recall_means.idxmax()
            worst_bucket = bucket_recall_means.idxmin()
            print(f"\n🏆 Best performing bucket: {best_bucket.upper()} (recall: {bucket_recall_means[best_bucket]:.4f})")
            print(f"⚠️  Worst performing bucket: {worst_bucket.upper()} (recall: {bucket_recall_means[worst_bucket]:.4f})")
        
        print("="*60)
    
    # Print filtering summary
    print("\n📊 PERSONALIZED FILTERING SUMMARY:")
    print("="*60)
    if len(recommendations_df) > 0:
        # Show overall statistics
        total_users = recommendations_df['customer_id'].nunique()
        total_recommendations = len(recommendations_df)
        product_column_used = recommendations_df['product_name_column_used'].iloc[0]
        
        print(f"Product name column used: {product_column_used}")
        print(f"Total users: {total_users:,}")
        print(f"Total personalized recommendations: {total_recommendations:,}")
        print(f"Average recommendations per user: {total_recommendations/total_users:.1f}")
        
        # Show k value distribution
        if 'personalized_k' in recommendations_df.columns:
            k_stats = recommendations_df['personalized_k'].describe()
            print(f"\nPersonalized k value distribution:")
            print(f"  Median k: {k_stats['50%']:.0f}")
            print(f"  Mean k: {k_stats['mean']:.1f}")
            print(f"  Range: {k_stats['min']:.0f} - {k_stats['max']:.0f}")
            
            # Show most common k values
            k_value_counts = recommendations_df['personalized_k'].value_counts().head(5)
            print(f"  Most common k values: {dict(k_value_counts)}")
        
        # Show breakdown by user type
        user_type_breakdown = recommendations_df['user_type'].value_counts()
        print(f"\nUser type breakdown:")
        for user_type, count in user_type_breakdown.items():
            threshold = recommendations_df[recommendations_df['user_type'] == user_type]['personalized_threshold'].iloc[0]
            print(f"  {user_type}: {count:,} recommendations (threshold ≥ {threshold})")
        
        # Show purchase probability distribution
        print(f"\nPurchase probability distribution:")
        print(f"  High confidence (≥0.6): {len(recommendations_df[recommendations_df['purchase_probability'] >= 0.6]):,}")
        print(f"  Medium confidence (0.4-0.59): {len(recommendations_df[(recommendations_df['purchase_probability'] >= 0.4) & (recommendations_df['purchase_probability'] < 0.6)]):,}")
        print(f"  Low confidence (0.3-0.39): {len(recommendations_df[(recommendations_df['purchase_probability'] >= 0.3) & (recommendations_df['purchase_probability'] < 0.4)]):,}")
        
        # Show sample recommendations for each user type
        print(f"\n📋 Sample recommendations by user type:")
        for user_type in ['Conservative', 'Moderate', 'Exploratory']:
            if user_type in user_type_breakdown.index:
                sample_user_recs = recommendations_df[recommendations_df['user_type'] == user_type].head(3)
                print(f"\n  {user_type} user examples:")
                for _, rec in sample_user_recs.iterrows():
                    print(f"    • {rec['product_name']} (Prob: {rec['purchase_probability']:.3f}, Type: {rec['recommendation_type']})")
    
    # Plot 1: Recall Distribution
    if len(user_metrics_df) > 0 and 'recall_at_k' in user_metrics_df.columns:
        # Create plotly histogram for recall distribution
        fig_1 = go.Figure()
        
        # Add histogram
        fig_1.add_trace(go.Histogram(
            x=user_metrics_df['recall_at_k'],
            nbinsx=30,
            opacity=0.7,
            marker_color='skyblue',
            marker_line_color='black',
            marker_line_width=1,
            name='Recall@10'
        ))
    
        # Calculate mean and median values
        mean_recall = user_metrics_df['recall_at_k'].mean()
        median_recall = user_metrics_df['recall_at_k'].median()
    
        # Add vertical line for mean with adjusted annotation position
        fig_1.add_vline(
            x=mean_recall,
            line_dash="dash",
            line_color="red",
            annotation_text=f"Mean: {mean_recall:.3f}",
            annotation_position="top left",
            annotation_font=dict(size=10, color="red")
        )
    
        # Add vertical line for median with adjusted annotation position
        fig_1.add_vline(
            x=median_recall,
            line_dash="dash",
            line_color="orange",
            annotation_text=f"Median: {median_recall:.3f}",
            annotation_position="top right",
            annotation_font=dict(size=10, color="orange")
        )
    
        # Update layout with additional margin
        fig_1.update_layout(
            title='Recall@10 Distribution',
            xaxis_title='Recall@10',
            yaxis_title='Frequency',
            showlegend=False,
            template='plotly_white',
            margin=dict(l=40, r=40, t=60, b=40)
        )
    
        print(f"✅ Recall distribution plot created (n={len(user_metrics_df)} evaluations)")
    
    else:
        print("⚠️ No recall data available for plotting")
        fig_1 = go.Figure()  # Empty figure as fallback
    
    # Plot 2: Recommendation Score Distribution  
    if len(recommendations_df) > 0 and 'recommendation_score' in recommendations_df.columns:
        # Create plotly histogram for recommendation score distribution
        fig_2 = go.Figure()
        
        # Add histogram
        fig_2.add_trace(go.Histogram(
            x=recommendations_df['recommendation_score'],
            nbinsx=30,
            opacity=0.7,
            marker_color='lightgreen',
            marker_line_color='black',
            marker_line_width=1,
            name='Recommendation Score'
        ))
    
        # Calculate mean and median values
        mean_score = recommendations_df['recommendation_score'].mean()
        median_score = recommendations_df['recommendation_score'].median()
    
        # Add vertical line for mean with adjusted annotation position
        fig_2.add_vline(
            x=mean_score,
            line_dash="dash",
            line_color="red",
            annotation_text=f"Mean: {mean_score:.3f}",
            annotation_position="top left",
            annotation_font=dict(size=10, color="red")
        )
    
        # Add vertical line for median with adjusted annotation position
        fig_2.add_vline(
            x=median_score,
            line_dash="dash",
            line_color="orange",
            annotation_text=f"Median: {median_score:.3f}",
            annotation_position="top right",
            annotation_font=dict(size=10, color="orange")
        )
    
        # Update layout with additional margin
        fig_2.update_layout(
            title='Recommendation Score Distribution',
            xaxis_title='Recommendation Score',
            yaxis_title='Frequency',
            showlegend=False,
            template='plotly_white',
            margin=dict(l=40, r=40, t=60, b=40)
        )
    
        print(f"✅ Recommendation score distribution plot created (n={len(recommendations_df)} recommendations)")
    
    else:
        print("⚠️ No recommendation score data available for plotting")
        fig_2 = go.Figure()  # Empty figure as fallback

import random

# Set a fixed random seed for reproducibility
RANDOM_SEED = 42
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

# 1. Get user segments from user_metrics_df
segment_map = {
    'repurchase': 'repurchase',
    'balanced': 'balanced',
    'discovery': 'discovery',
}

# Ensure user_metrics_df and recommendations_df exist
if 'user_metrics_df' in locals() and 'recommendations_df' in locals():
    # 2. Select 20 random users from each segment (if available)
    selected_users = []
    for segment in segment_map:
        users_in_segment = user_metrics_df[user_metrics_df['user_bucket'] == segment]['customer_id'].unique()
        if len(users_in_segment) > 20:
            sampled_users = random.sample(list(users_in_segment), 20)
        else:
            sampled_users = list(users_in_segment)
        selected_users.extend([(u, segment) for u in sampled_users])

    # 3. Get max delivery_date in customer_orders_rc
    max_date = pd.to_datetime(customer_orders_rc['delivery_date']).max()
    min_date = max_date - pd.Timedelta(days=6)  # last 7 days (inclusive)

    # 4. Prepare output rows with frequency counts and filter out empty purchases
    output_rows = []
    
    # Define the start date for frequency calculation (June 1st, 2025)
    frequency_start_date = pd.to_datetime('2025-06-01')
    
    for customer_id, segment in selected_users:
        # For each day in the last week
        for day in pd.date_range(min_date, max_date):
            # Actual purchased items for this specific day
            actual_items = customer_orders_rc[
                (customer_orders_rc['customer_id'] == customer_id) &
                (pd.to_datetime(customer_orders_rc['delivery_date']) == day)
            ]
            
            # Skip this day if no purchases were made
            if len(actual_items) == 0:
                continue
            
            # Get actual items for this day
            if PRODUCT_NAME_COLUMN in actual_items.columns:
                actual_items_set = set(actual_items[PRODUCT_NAME_COLUMN])
            else:
                # fallback to 'product_name' if present
                actual_items_set = set(actual_items['product_name']) if 'product_name' in actual_items.columns else set()
            
            # Calculate frequency counts for items purchased from June 1st, 2025 up to this specific delivery date
            user_purchases_up_to_date = customer_orders_rc[
                (customer_orders_rc['customer_id'] == customer_id) &
                (pd.to_datetime(customer_orders_rc['delivery_date']) >= frequency_start_date) &
                (pd.to_datetime(customer_orders_rc['delivery_date']) <= day)
            ]
            
            # Create frequency dictionary
            actual_items_frequency = {}
            if len(user_purchases_up_to_date) > 0:
                if PRODUCT_NAME_COLUMN in user_purchases_up_to_date.columns:
                    item_counts = user_purchases_up_to_date[PRODUCT_NAME_COLUMN].value_counts().to_dict()
                else:
                    # fallback to 'product_name' if present
                    item_counts = user_purchases_up_to_date['product_name'].value_counts().to_dict() if 'product_name' in user_purchases_up_to_date.columns else {}
                
                # Only include items that were actually purchased on this day
                for item in actual_items_set:
                    if item in item_counts:
                        actual_items_frequency[item] = item_counts[item]
                    else:
                        actual_items_frequency[item] = 1  # At least 1 for current purchase

            # Recommended items
            recs = recommendations_df[
                (recommendations_df['customer_id'] == customer_id) &
                (pd.to_datetime(recommendations_df['recommendation_date']) == day)
            ]
            recommended_items_set = set(recs['product_name']) if 'product_name' in recs.columns else set()

            # k for customer (use personalized_k from recommendations_df or user_metrics_df)
            k_val = None
            if not recs.empty and 'personalized_k' in recs.columns:
                k_val = recs['personalized_k'].iloc[0]
            elif 'personalized_k' in user_metrics_df.columns:
                k_row = user_metrics_df[user_metrics_df['customer_id'] == customer_id]
                if not k_row.empty:
                    k_val = k_row['personalized_k'].iloc[0]

            output_rows.append({
                'customer_id': customer_id,
                # 'customer_segment': segment,
                'delivery_date': day.strftime('%Y-%m-%d'),
                'actual_purchased_items': actual_items_frequency,  # Now a dictionary with item: frequency
                'recommended_items': list(recommended_items_set),
                'k': k_val
            })

    # 5. Create DataFrame and save
    segment_weekly_df = pd.DataFrame(output_rows)
    segment_weekly_df.to_csv('user_segment_weekly_comparison.csv', index=False)
    print(f"\n✅ Created user segment weekly comparison dataframe with frequency counts ({len(segment_weekly_df)} rows)")
    print(f"   📊 Actual purchased items now contain frequency counts from June 1st, 2025 onwards")
    print(f"   🗑️  Removed {len(selected_users) * 7 - len(segment_weekly_df)} rows where users had no purchases")
else:
    print("⚠️ user_metrics_df or recommendations_df not found. Please run the evaluation first.")

print("\n" + "="*70)
print("🎉 INTELLIGENT TEMPORAL PATTERN ANALYSIS SYSTEM READY!")
print("🧠 Key Features:")
print("   ✅ Individual user-item purchase pattern analysis")
print("   ✅ Intelligent temporal filtering (daily, weekly, monthly, irregular)")
print("   ✅ Pattern-based recommendation timing")
print("   ✅ Confidence scoring based on purchase consistency")
print("   ✅ Overdue item detection for high-priority recommendations")
print("="*70)

Helpers.save_output_dataset(context=context, output_name='last_one_week_recommendations-dynamick_d1', data_frame=recommendations_df)
Helpers.save_output_plotly_chart_as_json(context=context, chart_title='recall-distribution-dynamick_d1', plotly_fig=fig_1, group=None)
Helpers.save_output_plotly_chart_as_json(context=context, chart_title='recommendation-score-distribution-dynamick_d1', plotly_fig=fig_2, group=None)
Helpers.save_output_dataset(context=context, output_name='user_metrics-dynamick_d1', data_frame=user_metrics_df)
Helpers.save_output_dataset(context=context, output_name='segment_weekly_df_d1', data_frame=segment_weekly_df)