#!/usr/bin/env python3
"""
🚀 TEST STABLE PATTERN-AWARE RECOMMENDATION SYSTEM

Test script to verify the new stable system provides consistent performance
across evaluation dates without the dramatic volatility of the previous system.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def test_stable_system():
    """Test the new stable recommendation system"""
    
    print("🚀 TESTING STABLE PATTERN-AWARE RECOMMENDATION SYSTEM")
    print("="*70)
    print("🎯 Goal: Consistent performance across all evaluation dates")
    print("🎯 Target: ±10% variance maximum in precision/recall")
    print("="*70)
    
    # Import the new system
    try:
        from demo1 import HighPerformanceStableRecommendationSystem
        print("✅ Successfully imported HighPerformanceStableRecommendationSystem")
    except ImportError as e:
        print(f"❌ Failed to import new system: {e}")
        return
    
    # Load data
    print("\n📊 Loading test data...")
    try:
        customer_orders_rc = pd.read_csv('customer_orders_rc.csv')
        catalogue_df = pd.read_csv('catalogue.csv')
        repurchase_ratios_df = pd.read_csv('user_repurchase_ratios.csv')
        print(f"✅ Loaded {len(customer_orders_rc):,} orders, {len(catalogue_df):,} products")
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return
    
    # Initialize system
    print("\n🔧 Initializing High-Performance Stable System...")
    system = HighPerformanceStableRecommendationSystem(
        user_repurchase_ratios_df=repurchase_ratios_df,
        product_name_column='name'
    )
    
    # Test with a smaller date range for quick validation
    test_dates = [
        '2025-07-04', '2025-07-05', '2025-07-06', 
        '2025-07-07', '2025-07-08'
    ]
    
    print(f"\n🧪 Testing system stability across {len(test_dates)} dates...")
    print("📊 Evaluating performance consistency...")
    
    # Run evaluation
    try:
        results = system.evaluate_precision_recall_ultra_fast(
            customer_orders_rc, 
            test_days=len(test_dates), 
            use_personalized_k=True
        )
        
        print("\n🎯 STABILITY TEST RESULTS:")
        print("="*50)
        
        # Extract daily metrics from results
        daily_metrics = results.get('daily_metrics', {})
        
        if daily_metrics:
            precisions = []
            recalls = []
            
            for date, metrics in daily_metrics.items():
                precision = metrics.get('precision', 0)
                recall = metrics.get('recall', 0)
                precisions.append(precision)
                recalls.append(recall)
                print(f"📅 {date}: P@k: {precision:.3f}, R@k: {recall:.3f}")
            
            # Calculate stability metrics
            if len(precisions) > 1:
                precision_variance = np.var(precisions)
                recall_variance = np.var(recalls)
                precision_cv = np.std(precisions) / np.mean(precisions) if np.mean(precisions) > 0 else 0
                recall_cv = np.std(recalls) / np.mean(recalls) if np.mean(recalls) > 0 else 0
                
                print(f"\n📊 STABILITY ANALYSIS:")
                print(f"   Precision CV: {precision_cv:.3f} (lower is better)")
                print(f"   Recall CV: {recall_cv:.3f} (lower is better)")
                print(f"   Precision range: {min(precisions):.3f} - {max(precisions):.3f}")
                print(f"   Recall range: {min(recalls):.3f} - {max(recalls):.3f}")
                
                # Success criteria: CV < 0.3 (30% coefficient of variation)
                if precision_cv < 0.3 and recall_cv < 0.3:
                    print("✅ STABILITY TEST PASSED: Low variance in performance")
                else:
                    print("⚠️ STABILITY TEST NEEDS IMPROVEMENT: High variance detected")
            
        else:
            print("⚠️ No daily metrics available for stability analysis")
        
        # Overall performance
        overall_precision = results.get('precision', 0)
        overall_recall = results.get('recall', 0)
        
        print(f"\n🏆 OVERALL PERFORMANCE:")
        print(f"   Average Precision@k: {overall_precision:.3f}")
        print(f"   Average Recall@k: {overall_recall:.3f}")
        print(f"   Hit Rate: {results.get('hit_rate', 0):.3f}")
        
        # Performance targets
        if overall_precision > 0.15 and overall_recall > 0.20:
            print("✅ PERFORMANCE TEST PASSED: Meets minimum thresholds")
        else:
            print("⚠️ PERFORMANCE TEST NEEDS IMPROVEMENT: Below target thresholds")
        
        print("\n🎉 STABLE SYSTEM TEST COMPLETED!")
        return results
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_stable_system()
