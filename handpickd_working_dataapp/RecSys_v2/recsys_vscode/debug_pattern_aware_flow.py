#!/usr/bin/env python3
"""
Debug script to trace what happens to our Enhanced Pattern-Aware recommendations
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

def debug_pattern_aware_flow():
    """Debug the flow from pattern-aware generation to final segment_weekly_df"""
    
    print("🔍 Debugging Enhanced Pattern-Aware Recommendation Flow")
    print("=" * 70)
    
    try:
        # Import the recommendation system
        from demo1 import UltraOptimizedRecommendationSystem
        
        # Load data
        print("📊 Loading data...")
        catalogue_df = pd.read_csv('catalogue_rc_with_parent_names.csv')
        customer_orders_df = pd.read_csv('customer_orders.csv')
        repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
        
        # Initialize system
        rec_system = UltraOptimizedRecommendationSystem(
            user_repurchase_ratios_df=repurchase_ratios_df,
            product_name_column='name'
        )
        
        # Precompute features
        rec_system.precompute_static_features(customer_orders_df, catalogue_df)
        
        # Convert dates
        customer_orders_df['delivery_date'] = pd.to_datetime(customer_orders_df['delivery_date'])
        
        # Test with 2 consecutive dates
        test_dates = [
            pd.Timestamp('2025-07-07'),
            pd.Timestamp('2025-07-08')
        ]
        
        # Test users
        test_users = ['570fa519-af38-458e-851b-6a73ce2ff756', '4bcb4707-946a-45e7-98ef-8a669f362078']
        
        print(f"🧪 Testing pattern-aware flow for {len(test_users)} users across {len(test_dates)} dates")
        
        results_by_stage = {}
        
        for eval_date in test_dates:
            print(f"\n📅 Testing {eval_date.strftime('%Y-%m-%d')}...")
            
            # Filter training data
            train_data = customer_orders_df[customer_orders_df['delivery_date'] < eval_date]
            
            # Build matrices
            model_data = rec_system.build_matrices_ultra_fast(train_data, eval_date)
            user_profiles = rec_system.build_user_profiles_lightning_fast(
                model_data['user_item_matrix'], 
                model_data['user_item_qty_matrix']
            )
            
            # Analyze patterns
            rec_system.analyze_daily_purchase_patterns(customer_orders_df, eval_date)
            rec_system.analyze_user_purchase_patterns(customer_orders_df, eval_date)
            
            date_results = {}
            
            for user_id in test_users:
                if user_id not in user_profiles:
                    continue
                
                print(f"\n   👤 User {user_id[:8]}...")
                
                # Calculate personalized k
                user_k = rec_system.calculate_personalized_k_for_user(user_id, customer_orders_df)
                print(f"      📊 Personalized k: {user_k}")
                
                # STAGE 1: Generate Enhanced Pattern-Aware recommendations
                pattern_aware_recs = rec_system.get_recommendations_with_intelligent_deduplication(
                    user_id, user_profiles[user_id], model_data['similarity_dict'], eval_date, user_k
                )
                
                stage1_items = [r['product_name'] for r in pattern_aware_recs]
                print(f"      🎯 Stage 1 (Pattern-Aware): {stage1_items}")
                
                # STAGE 2: Apply additional filtering (like in the main evaluation)
                user_repurchase_ratio = rec_system.get_user_repurchase_ratio(user_id)
                
                # Apply the same thresholding as in the main evaluation
                if user_repurchase_ratio >= 0.8:
                    personalized_threshold = 0.15
                    user_type = 'Conservative'
                elif user_repurchase_ratio >= 0.5:
                    personalized_threshold = 0.10
                    user_type = 'Moderate'
                else:
                    personalized_threshold = 0.05
                    user_type = 'Exploratory'
                
                print(f"      👤 User type: {user_type} (threshold: {personalized_threshold})")
                
                # Apply the same filtering as in the main evaluation
                filtered_recs = []
                for rec in pattern_aware_recs:
                    purchase_probability = rec_system.calculate_purchase_probability(
                        rec, user_repurchase_ratio, user_id
                    )
                    
                    if purchase_probability >= personalized_threshold:
                        filtered_recs.append(rec['product_name'])
                
                stage2_items = filtered_recs
                print(f"      🔍 Stage 2 (Filtered): {stage2_items}")
                
                # Compare stages
                stage1_set = set(stage1_items)
                stage2_set = set(stage2_items)
                
                if stage1_set != stage2_set:
                    removed = stage1_set - stage2_set
                    added = stage2_set - stage1_set
                    print(f"      ⚠️ FILTERING CHANGED RECOMMENDATIONS!")
                    if removed:
                        print(f"         ➖ Removed: {list(removed)}")
                    if added:
                        print(f"         ➕ Added: {list(added)}")
                else:
                    print(f"      ✅ No change from filtering")
                
                date_results[user_id] = {
                    'stage1_pattern_aware': stage1_items,
                    'stage2_filtered': stage2_items,
                    'user_k': user_k,
                    'user_type': user_type,
                    'threshold': personalized_threshold
                }
            
            results_by_stage[eval_date] = date_results
        
        # Analyze cross-date patterns
        print(f"\n📊 Cross-Date Pattern Analysis:")
        print("=" * 40)
        
        for user_id in test_users:
            print(f"\n👤 User {user_id[:8]}...")
            
            user_results = []
            for date in test_dates:
                if date in results_by_stage and user_id in results_by_stage[date]:
                    user_results.append(results_by_stage[date][user_id])
            
            if len(user_results) >= 2:
                # Compare Stage 1 (Pattern-Aware) across dates
                stage1_day1 = set(user_results[0]['stage1_pattern_aware'])
                stage1_day2 = set(user_results[1]['stage1_pattern_aware'])
                stage1_similarity = len(stage1_day1.intersection(stage1_day2)) / len(stage1_day1.union(stage1_day2)) if len(stage1_day1.union(stage1_day2)) > 0 else 0
                
                # Compare Stage 2 (Filtered) across dates
                stage2_day1 = set(user_results[0]['stage2_filtered'])
                stage2_day2 = set(user_results[1]['stage2_filtered'])
                stage2_similarity = len(stage2_day1.intersection(stage2_day2)) / len(stage2_day1.union(stage2_day2)) if len(stage2_day1.union(stage2_day2)) > 0 else 0
                
                print(f"   📊 Pattern-Aware similarity: {stage1_similarity:.1%}")
                print(f"   🔍 Filtered similarity: {stage2_similarity:.1%}")
                
                if stage2_similarity > stage1_similarity:
                    print(f"   ⚠️ FILTERING IS REDUCING DIVERSITY!")
                    print(f"      Pattern-aware created variety, but filtering made it more similar")
                elif stage1_similarity > 0.8:
                    print(f"   ⚠️ PATTERN-AWARE SYSTEM NOT CREATING ENOUGH VARIETY!")
                else:
                    print(f"   ✅ Pattern-aware system working correctly")
                
                # Show the actual recommendations
                print(f"   📅 {test_dates[0].strftime('%m-%d')}: {user_results[0]['stage2_filtered']}")
                print(f"   📅 {test_dates[1].strftime('%m-%d')}: {user_results[1]['stage2_filtered']}")
        
        # Check what's in the actual CSV
        print(f"\n📁 Checking actual CSV content:")
        print("=" * 30)
        
        try:
            csv_df = pd.read_csv('user_segment_weekly_comparison.csv')
            for user_id in test_users:
                user_csv = csv_df[csv_df['customer_id'] == user_id]
                if len(user_csv) > 0:
                    print(f"\n👤 User {user_id[:8]} in CSV:")
                    for _, row in user_csv.head(2).iterrows():
                        print(f"   📅 {row['delivery_date']}: {row['recommended_items']}")
        except FileNotFoundError:
            print("   ❌ CSV file not found")
        
        print(f"\n💡 Diagnosis:")
        print("=" * 15)
        print("1. Check if Pattern-Aware system is creating variety")
        print("2. Check if additional filtering is removing variety")
        print("3. Check if CSV generation is using the right data")
        
    except Exception as e:
        print(f"❌ Error during debugging: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_pattern_aware_flow()
