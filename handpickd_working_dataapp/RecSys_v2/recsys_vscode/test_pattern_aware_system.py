#!/usr/bin/env python3
"""
Test script for the Enhanced Pattern-Aware Recommendation System
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

def test_pattern_aware_system():
    """Test the enhanced pattern-aware recommendation system"""
    
    print("🧠 Testing Enhanced Pattern-Aware Recommendation System")
    print("=" * 70)
    
    try:
        # Import the recommendation system
        from demo1 import UltraOptimizedRecommendationSystem
        
        # Load existing data
        print("📊 Loading existing data...")
        
        try:
            catalogue_df = pd.read_csv('catalogue_rc_with_parent_names.csv')
            customer_orders_df = pd.read_csv('customer_orders.csv')
            repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
            
            print(f"   ✅ Loaded catalogue: {len(catalogue_df)} items")
            print(f"   ✅ Loaded orders: {len(customer_orders_df)} orders")
            print(f"   ✅ Loaded repurchase ratios: {len(repurchase_ratios_df)} users")
        except FileNotFoundError as e:
            print(f"   ❌ Could not load data files: {e}")
            return
        
        # Initialize the recommendation system
        rec_system = UltraOptimizedRecommendationSystem(
            user_repurchase_ratios_df=repurchase_ratios_df,
            product_name_column='name'
        )
        
        # Precompute static features
        rec_system.precompute_static_features(customer_orders_df, catalogue_df)
        
        # Convert dates
        customer_orders_df['delivery_date'] = pd.to_datetime(customer_orders_df['delivery_date'])
        
        # Test with recent dates
        max_date = customer_orders_df['delivery_date'].max()
        test_dates = [max_date - timedelta(days=i) for i in range(1, -1, -1)]  # Last 2 days
        
        print(f"\n🧪 Testing with dates: {[d.strftime('%Y-%m-%d') for d in test_dates]}")
        
        # Select test users (including our debug users)
        debug_users = ['570fa519', '4bcb4707']
        frequent_users = customer_orders_df['customer_id'].value_counts().head(10).index.tolist()
        
        test_users = []
        for debug_prefix in debug_users:
            debug_user = next((uid for uid in frequent_users if uid.startswith(debug_prefix)), None)
            if debug_user:
                test_users.append(debug_user)
        
        # Add a few more users for comparison
        test_users.extend([u for u in frequent_users[:3] if u not in test_users])
        
        print(f"👥 Testing with users: {[u[:8] + '...' for u in test_users]}")
        
        results = {
            'baseline': {},
            'deduplication_only': {},
            'pattern_aware_full': {}
        }
        
        for eval_date in test_dates:
            print(f"\n📅 Testing {eval_date.strftime('%Y-%m-%d')}...")
            
            # Filter training data
            train_data = customer_orders_df[customer_orders_df['delivery_date'] < eval_date]
            
            if len(train_data) == 0:
                continue
            
            # Build matrices
            model_data = rec_system.build_matrices_ultra_fast(train_data, eval_date)
            user_profiles = rec_system.build_user_profiles_lightning_fast(
                model_data['user_item_matrix'], 
                model_data['user_item_qty_matrix']
            )
            
            # Analyze patterns
            rec_system.analyze_daily_purchase_patterns(customer_orders_df, eval_date)
            rec_system.analyze_user_purchase_patterns(customer_orders_df, eval_date)
            
            date_results = {
                'baseline': {},
                'deduplication_only': {},
                'pattern_aware_full': {}
            }
            
            for user_id in test_users:
                if user_id not in user_profiles:
                    continue
                
                print(f"\n   👤 Testing user {user_id[:8]}...")
                
                # Test 1: Baseline (no deduplication, no pattern-aware)
                recs_baseline = rec_system.get_recommendations_with_intelligent_deduplication(
                    user_id, user_profiles[user_id], model_data['similarity_dict'], 
                    eval_date, 8, enable_deduplication=False, enable_pattern_aware=False
                )
                
                # Test 2: Deduplication only
                recs_dedup_only = rec_system.get_recommendations_with_intelligent_deduplication(
                    user_id, user_profiles[user_id], model_data['similarity_dict'], 
                    eval_date, 8, enable_deduplication=True, enable_pattern_aware=False
                )
                
                # Test 3: Full pattern-aware system
                recs_pattern_aware = rec_system.get_recommendations_with_intelligent_deduplication(
                    user_id, user_profiles[user_id], model_data['similarity_dict'], 
                    eval_date, 8, enable_deduplication=True, enable_pattern_aware=True
                )
                
                date_results['baseline'][user_id] = [r['product_name'] for r in recs_baseline]
                date_results['deduplication_only'][user_id] = [r['product_name'] for r in recs_dedup_only]
                date_results['pattern_aware_full'][user_id] = [r['product_name'] for r in recs_pattern_aware]
                
                print(f"      📊 Baseline: {len(recs_baseline)} items")
                print(f"      🧠 Dedup only: {len(recs_dedup_only)} items")
                print(f"      🎯 Pattern-aware: {len(recs_pattern_aware)} items")
            
            results['baseline'][eval_date] = date_results['baseline']
            results['deduplication_only'][eval_date] = date_results['deduplication_only']
            results['pattern_aware_full'][eval_date] = date_results['pattern_aware_full']
            
            print(f"   ✅ Generated recommendations for {len(date_results['baseline'])} users")
        
        # Analyze the results
        print(f"\n📊 Analysis of Pattern-Aware System Impact:")
        print("=" * 60)
        
        if len(test_dates) >= 2:
            for user_id in test_users:
                print(f"\n👤 User: {user_id[:8]}...")
                
                # Show user's purchase patterns if available
                if user_id in rec_system.user_purchase_patterns:
                    patterns = rec_system.user_purchase_patterns[user_id]
                    print(f"   📊 Purchase pattern: {patterns['total_orders']} orders analyzed")
                    
                    if patterns['day_patterns']:
                        most_active_day = max(patterns['day_patterns'].items(), key=lambda x: x[1]['item_count'])
                        print(f"   📅 Most active day: {most_active_day[0]} ({most_active_day[1]['item_count']} items)")
                        
                        if most_active_day[1]['top_categories']:
                            top_cat = list(most_active_day[1]['top_categories'].keys())[0]
                            print(f"   🛒 Top category on {most_active_day[0]}: {top_cat}")
                
                # Compare consecutive day similarities
                for i in range(len(test_dates) - 1):
                    date1, date2 = test_dates[i], test_dates[i + 1]
                    
                    if (user_id in results['baseline'].get(date1, {}) and 
                        user_id in results['baseline'].get(date2, {})):
                        
                        # Calculate similarities for each approach
                        approaches = ['baseline', 'deduplication_only', 'pattern_aware_full']
                        similarities = {}
                        
                        for approach in approaches:
                            recs1 = set(results[approach][date1][user_id])
                            recs2 = set(results[approach][date2][user_id])
                            overlap = len(recs1.intersection(recs2))
                            similarity = overlap / len(recs1.union(recs2)) if len(recs1.union(recs2)) > 0 else 0
                            similarities[approach] = similarity
                        
                        print(f"   📅 {date1.strftime('%m-%d')} → {date2.strftime('%m-%d')} similarity:")
                        print(f"      Baseline: {similarities['baseline']:.1%}")
                        print(f"      Dedup only: {similarities['deduplication_only']:.1%}")
                        print(f"      Pattern-aware: {similarities['pattern_aware_full']:.1%}")
                        
                        # Show improvement
                        dedup_improvement = similarities['baseline'] - similarities['deduplication_only']
                        pattern_improvement = similarities['baseline'] - similarities['pattern_aware_full']
                        
                        if pattern_improvement > dedup_improvement:
                            print(f"      ✅ Pattern-aware best: {pattern_improvement:.1%} improvement over baseline")
                        elif dedup_improvement > 0:
                            print(f"      ✅ Deduplication helped: {dedup_improvement:.1%} improvement")
                        else:
                            print(f"      ➡️ Similar performance across approaches")
        
        # Show sample recommendations
        print(f"\n📝 Sample Recommendations Comparison:")
        print("=" * 50)
        
        if test_dates and test_users:
            sample_date = test_dates[-1]  # Last date
            sample_user = test_users[0]   # First user
            
            if (sample_user in results['baseline'].get(sample_date, {}) and
                sample_user in results['pattern_aware_full'].get(sample_date, {})):
                
                baseline_recs = results['baseline'][sample_date][sample_user]
                pattern_recs = results['pattern_aware_full'][sample_date][sample_user]
                
                print(f"👤 User: {sample_user[:8]}... on {sample_date.strftime('%Y-%m-%d')}")
                print(f"   Baseline: {baseline_recs}")
                print(f"   Pattern-aware: {pattern_recs}")
                
                # Show differences
                baseline_set = set(baseline_recs)
                pattern_set = set(pattern_recs)
                
                only_baseline = baseline_set - pattern_set
                only_pattern = pattern_set - baseline_set
                
                if only_baseline:
                    print(f"   ➖ Removed by pattern-aware: {list(only_baseline)}")
                if only_pattern:
                    print(f"   ➕ Added by pattern-aware: {list(only_pattern)}")
        
        print(f"\n🎉 Pattern-aware system testing completed!")
        print(f"💡 The enhanced system provides contextual recommendations based on user behavior patterns.")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pattern_aware_system()
