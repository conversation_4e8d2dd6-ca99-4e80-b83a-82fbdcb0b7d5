#!/usr/bin/env python3
"""
🚀 RUN FRESH DIVERSITY-FIRST RECOMMENDATION SYSTEM

Test script to run the completely new recommendation system and generate
all required outputs with focus on both performance and diversity.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def main():
    """Run the fresh recommendation system"""
    
    print("🚀 RUNNING FRESH DIVERSITY-FIRST RECOMMENDATION SYSTEM")
    print("="*70)
    print("🎯 Goals:")
    print("   1. Achieve 40%+ precision and recall")
    print("   2. Generate diverse recommendations across dates")
    print("   3. Export identical output format as existing system")
    print("="*70)
    
    # Load data
    print("\n📊 Loading data...")
    try:
        customer_orders_df = pd.read_csv('customer_orders.csv')
        catalogue_df = pd.read_csv('catalogue_rc.csv')
        repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
        
        print(f"✅ Loaded {len(customer_orders_df):,} orders")
        print(f"✅ Loaded {len(catalogue_df):,} catalogue items")
        print(f"✅ Loaded {len(repurchase_ratios_df):,} user repurchase ratios")
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return
    
    # Initialize fresh system
    print("\n🔧 Initializing Fresh Diversity-First System...")
    from fresh_recommendation_system import FreshDiversityFirstRecommendationSystem
    
    system = FreshDiversityFirstRecommendationSystem(
        user_repurchase_ratios_df=repurchase_ratios_df,
        product_name_column='product_name'
    )
    
    # Run evaluation
    print("\n🚀 Running fresh system evaluation...")
    start_time = datetime.now()
    
    try:
        results = system.evaluate_multiple_dates(
            customer_orders_df=customer_orders_df,
            catalogue_df=catalogue_df,
            test_days=7
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"\n⏱️  Evaluation completed in {duration:.1f} seconds")
        
        # Extract results
        recommendations_df = results['recommendations_df']
        user_metrics_df = results['user_metrics_df']
        segment_weekly_df = results['segment_weekly_df']
        fig_1 = results['fig_1']
        fig_2 = results['fig_2']
        
        # Display performance summary
        print(f"\n🏆 FRESH SYSTEM PERFORMANCE SUMMARY:")
        print(f"   Precision@k: {results['precision']:.3f}")
        print(f"   Recall@k: {results['recall']:.3f}")
        print(f"   Hit Rate: {results['hit_rate']:.3f}")
        
        # Check if targets are met
        if results['precision'] >= 0.4 and results['recall'] >= 0.4:
            print("✅ PERFORMANCE TARGETS ACHIEVED!")
        else:
            print("⚠️  Performance targets not yet reached")
            print(f"   Precision gap: {0.4 - results['precision']:.3f}")
            print(f"   Recall gap: {0.4 - results['recall']:.3f}")
        
        # Analyze diversity in segment_weekly_df
        print(f"\n🔍 DIVERSITY ANALYSIS:")
        analyze_recommendation_diversity(segment_weekly_df)
        
        # Save results
        print(f"\n💾 Saving results...")
        recommendations_df.to_csv('fresh_recommendations_df.csv', index=False)
        user_metrics_df.to_csv('fresh_user_metrics_df.csv', index=False)
        segment_weekly_df.to_csv('fresh_segment_weekly_df.csv', index=False)
        
        print(f"✅ Saved fresh_recommendations_df.csv ({len(recommendations_df):,} rows)")
        print(f"✅ Saved fresh_user_metrics_df.csv ({len(user_metrics_df):,} rows)")
        print(f"✅ Saved fresh_segment_weekly_df.csv ({len(segment_weekly_df):,} rows)")
        
        # Display sample recommendations
        print(f"\n📋 SAMPLE FRESH RECOMMENDATIONS:")
        display_sample_recommendations(segment_weekly_df)
        
        print(f"\n🎉 FRESH DIVERSITY-FIRST SYSTEM EVALUATION COMPLETED!")
        
        return results
        
    except Exception as e:
        print(f"❌ Error during evaluation: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_recommendation_diversity(segment_weekly_df):
    """Analyze diversity in recommendations across dates"""
    
    # Group by customer and check for diversity across dates
    diversity_stats = []
    
    for customer_id in segment_weekly_df['customer_id'].unique():
        customer_data = segment_weekly_df[
            segment_weekly_df['customer_id'] == customer_id
        ].sort_values('delivery_date')
        
        if len(customer_data) < 2:
            continue
        
        # Check for identical recommendations across dates
        rec_lists = customer_data['recommended_items'].tolist()
        
        # Count unique recommendation sets
        unique_sets = len(set(str(rec_list) for rec_list in rec_lists))
        total_dates = len(rec_lists)
        
        diversity_score = unique_sets / total_dates if total_dates > 0 else 0
        
        diversity_stats.append({
            'customer_id': customer_id,
            'total_dates': total_dates,
            'unique_recommendation_sets': unique_sets,
            'diversity_score': diversity_score
        })
    
    if diversity_stats:
        diversity_df = pd.DataFrame(diversity_stats)
        
        avg_diversity = diversity_df['diversity_score'].mean()
        perfect_diversity_pct = (diversity_df['diversity_score'] == 1.0).mean() * 100
        
        print(f"   Average diversity score: {avg_diversity:.3f}")
        print(f"   Users with perfect diversity: {perfect_diversity_pct:.1f}%")
        
        if avg_diversity > 0.8:
            print("✅ EXCELLENT diversity achieved!")
        elif avg_diversity > 0.6:
            print("✅ GOOD diversity achieved!")
        else:
            print("⚠️  Diversity needs improvement")
    else:
        print("⚠️  No diversity data available")

def display_sample_recommendations(segment_weekly_df):
    """Display sample recommendations to show diversity"""
    
    # Get a sample customer with multiple dates
    customer_counts = segment_weekly_df['customer_id'].value_counts()
    sample_customers = customer_counts[customer_counts >= 3].head(3).index.tolist()
    
    for customer_id in sample_customers:
        customer_data = segment_weekly_df[
            segment_weekly_df['customer_id'] == customer_id
        ].sort_values('delivery_date')
        
        print(f"\n👤 Customer {customer_id[:8]}...:")
        
        for _, row in customer_data.head(4).iterrows():  # Show first 4 dates
            rec_items = row['recommended_items'][:5]  # Show first 5 recommendations
            print(f"   📅 {row['delivery_date']}: {rec_items}")

if __name__ == "__main__":
    main()
