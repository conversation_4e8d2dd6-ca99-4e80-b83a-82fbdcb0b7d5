#!/usr/bin/env python3
"""
Test script for the Intelligent Recommendation Deduplication System
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

def test_intelligent_deduplication():
    """Test the intelligent deduplication system"""
    
    print("🧠 Testing Intelligent Recommendation Deduplication System")
    print("=" * 70)
    
    try:
        # Import the recommendation system
        from demo1 import UltraOptimizedRecommendationSystem
        
        # Create sample data for testing
        print("📊 Creating test dataset...")
        
        # Create sample dates (last 20 days)
        end_date = datetime(2025, 7, 10)
        start_date = end_date - timedelta(days=19)
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # Create sample customers and products
        customers = [f'customer_{i}' for i in range(1, 6)]
        products = [
            'Milk', 'Bread', 'Eggs', 'Banana', 'Apple', 'Tomato', 'Onion', 'Potato',
            'Rice', 'Chicken', 'Yogurt', 'Cheese', 'Spinach', 'Carrot', 'Orange'
        ]
        
        # Create sample orders with realistic daily patterns
        orders_data = []
        order_id = 1
        
        for customer in customers:
            # Define daily essentials for each customer (items they buy frequently)
            if customer == 'customer_1':
                daily_essentials = ['Milk', 'Bread']  # Buys milk and bread almost daily
                frequent_items = ['Banana', 'Yogurt']  # Buys these often but not daily
            elif customer == 'customer_2':
                daily_essentials = ['Eggs', 'Milk']
                frequent_items = ['Apple', 'Tomato']
            else:
                daily_essentials = ['Milk']
                frequent_items = ['Banana', 'Apple']
            
            for date in dates:
                # Simulate daily essential purchases (80-90% probability)
                for item in daily_essentials:
                    if np.random.random() < 0.85:  # 85% chance of buying daily essentials
                        orders_data.append({
                            'customer_id': customer,
                            'sku_code': item,
                            'delivery_date': date,
                            'ordered_qty': np.random.randint(1, 3),
                            'display_order_id': f'order_{order_id}'
                        })
                        order_id += 1
                
                # Simulate frequent item purchases (40-60% probability)
                for item in frequent_items:
                    if np.random.random() < 0.5:  # 50% chance
                        orders_data.append({
                            'customer_id': customer,
                            'sku_code': item,
                            'delivery_date': date,
                            'ordered_qty': np.random.randint(1, 3),
                            'display_order_id': f'order_{order_id}'
                        })
                        order_id += 1
                
                # Simulate occasional purchases (10-20% probability)
                occasional_items = [p for p in products if p not in daily_essentials and p not in frequent_items]
                for item in np.random.choice(occasional_items, size=np.random.randint(0, 3), replace=False):
                    if np.random.random() < 0.15:  # 15% chance
                        orders_data.append({
                            'customer_id': customer,
                            'sku_code': item,
                            'delivery_date': date,
                            'ordered_qty': np.random.randint(1, 2),
                            'display_order_id': f'order_{order_id}'
                        })
                        order_id += 1
        
        customer_orders_df = pd.DataFrame(orders_data)
        
        # Create sample catalogue
        catalogue_data = []
        categories = ['Dairy', 'Bakery', 'Produce', 'Meat', 'Grains']
        for i, product in enumerate(products):
            catalogue_data.append({
                'sku_code': product,
                'name': product,
                'category_name': categories[i % len(categories)],
                'sku_parent_name': product
            })
        
        catalogue_df = pd.DataFrame(catalogue_data)
        
        # Create sample repurchase ratios
        repurchase_data = []
        for customer in customers:
            repurchase_data.append({
                'customer_id': customer,
                'repurchase_ratio': np.random.uniform(0.6, 0.9)
            })
        
        repurchase_ratios_df = pd.DataFrame(repurchase_data)
        
        print(f"📊 Created test data:")
        print(f"   - Orders: {len(customer_orders_df)} orders over {len(dates)} days")
        print(f"   - Customers: {len(customers)}")
        print(f"   - Products: {len(products)}")
        
        # Initialize the recommendation system
        rec_system = UltraOptimizedRecommendationSystem(
            user_repurchase_ratios_df=repurchase_ratios_df,
            product_name_column='name'
        )
        
        # Precompute static features
        rec_system.precompute_static_features(customer_orders_df, catalogue_df)
        
        # Test the intelligent deduplication system
        print(f"\n🧠 Testing Intelligent Deduplication System:")
        print("=" * 50)
        
        # Test with the last few days
        test_dates = [end_date - timedelta(days=i) for i in range(2, -1, -1)]  # Last 3 days
        
        test_customer = 'customer_1'
        print(f"👤 Testing with customer: {test_customer}")
        
        recommendations_by_date = {}
        
        for eval_date in test_dates:
            print(f"\n📅 Generating recommendations for {eval_date.strftime('%Y-%m-%d')}...")
            
            # Filter training data
            train_data = customer_orders_df[customer_orders_df['delivery_date'] < eval_date]
            
            if len(train_data) == 0:
                print(f"   ⚠️ No training data for {eval_date}")
                continue
            
            # Build matrices
            model_data = rec_system.build_matrices_ultra_fast(train_data, eval_date)
            user_profiles = rec_system.build_user_profiles_lightning_fast(
                model_data['user_item_matrix'], 
                model_data['user_item_qty_matrix']
            )
            
            if test_customer not in user_profiles:
                print(f"   ⚠️ Customer {test_customer} not found in user profiles")
                continue
            
            # Test both regular and intelligent deduplication
            print(f"   🔄 Testing regular recommendations...")
            regular_recs = rec_system.get_recommendations_high_recall_ensemble(
                test_customer, user_profiles[test_customer], model_data['similarity_dict'], 10
            )
            
            print(f"   🧠 Testing intelligent deduplication...")
            smart_recs = rec_system.get_recommendations_with_intelligent_deduplication(
                test_customer, user_profiles[test_customer], model_data['similarity_dict'], eval_date, 10
            )
            
            recommendations_by_date[eval_date] = {
                'regular': [rec['product_name'] for rec in regular_recs],
                'smart': [rec['product_name'] for rec in smart_recs]
            }
            
            print(f"   📊 Regular recommendations: {len(regular_recs)} items")
            print(f"   🧠 Smart recommendations: {len(smart_recs)} items")
            print(f"   📝 Regular sample: {recommendations_by_date[eval_date]['regular'][:5]}")
            print(f"   🎯 Smart sample: {recommendations_by_date[eval_date]['smart'][:5]}")
        
        # Analyze the results
        print(f"\n🔍 Analysis of Deduplication Effectiveness:")
        print("=" * 50)
        
        if len(recommendations_by_date) >= 2:
            dates = list(recommendations_by_date.keys())
            
            for i in range(len(dates) - 1):
                date1, date2 = dates[i], dates[i + 1]
                
                # Compare regular recommendations
                regular_1 = set(recommendations_by_date[date1]['regular'])
                regular_2 = set(recommendations_by_date[date2]['regular'])
                regular_overlap = len(regular_1.intersection(regular_2))
                regular_similarity = regular_overlap / len(regular_1.union(regular_2)) if len(regular_1.union(regular_2)) > 0 else 0
                
                # Compare smart recommendations
                smart_1 = set(recommendations_by_date[date1]['smart'])
                smart_2 = set(recommendations_by_date[date2]['smart'])
                smart_overlap = len(smart_1.intersection(smart_2))
                smart_similarity = smart_overlap / len(smart_1.union(smart_2)) if len(smart_1.union(smart_2)) > 0 else 0
                
                print(f"📊 {date1.strftime('%m-%d')} vs {date2.strftime('%m-%d')}:")
                print(f"   Regular recommendations similarity: {regular_similarity:.1%} ({regular_overlap} common items)")
                print(f"   Smart recommendations similarity: {smart_similarity:.1%} ({smart_overlap} common items)")
                
                improvement = regular_similarity - smart_similarity
                if improvement > 0:
                    print(f"   ✅ Deduplication reduced similarity by {improvement:.1%}")
                else:
                    print(f"   ⚠️ Deduplication increased similarity by {abs(improvement):.1%}")
                print()
        
        # Check daily essentials detection
        print(f"🎯 Daily Essentials Detection:")
        print("=" * 30)
        
        if test_customer in rec_system.user_daily_essentials:
            essentials = rec_system.user_daily_essentials[test_customer]
            print(f"   👤 {test_customer} daily essentials: {len(essentials)} items")
            for item, freq in essentials.items():
                print(f"      • {item}: {freq:.1%} purchase frequency")
        else:
            print(f"   ⚠️ No daily essentials detected for {test_customer}")
        
        print(f"\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_intelligent_deduplication()
