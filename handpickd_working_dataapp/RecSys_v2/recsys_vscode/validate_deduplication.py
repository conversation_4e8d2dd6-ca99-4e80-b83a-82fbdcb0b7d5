#!/usr/bin/env python3
"""
Validation script for the Intelligent Recommendation Deduplication System
Compares recommendations with and without deduplication to show improvements
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

def validate_deduplication_system():
    """Validate the deduplication system by comparing with/without deduplication"""
    
    print("🔍 Validating Intelligent Recommendation Deduplication System")
    print("=" * 70)
    
    try:
        # Import the recommendation system
        from demo1 import UltraOptimizedRecommendationSystem
        
        # Load existing data
        print("📊 Loading existing data...")
        
        # Read the CSV files
        try:
            catalogue_df = pd.read_csv('catalogue_rc_with_parent_names.csv')
            customer_orders_df = pd.read_csv('customer_orders.csv')
            repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
            
            print(f"   ✅ Loaded catalogue: {len(catalogue_df)} items")
            print(f"   ✅ Loaded orders: {len(customer_orders_df)} orders")
            print(f"   ✅ Loaded repurchase ratios: {len(repurchase_ratios_df)} users")
        except FileNotFoundError as e:
            print(f"   ❌ Could not load data files: {e}")
            return
        
        # Initialize the recommendation system
        rec_system = UltraOptimizedRecommendationSystem(
            user_repurchase_ratios_df=repurchase_ratios_df,
            product_name_column='name'
        )
        
        # Precompute static features
        rec_system.precompute_static_features(customer_orders_df, catalogue_df)
        
        # Convert dates
        customer_orders_df['delivery_date'] = pd.to_datetime(customer_orders_df['delivery_date'])
        
        # Test with recent dates
        max_date = customer_orders_df['delivery_date'].max()
        test_dates = [max_date - timedelta(days=i) for i in range(2, -1, -1)]  # Last 3 days
        
        print(f"\n🧪 Testing with dates: {[d.strftime('%Y-%m-%d') for d in test_dates]}")
        
        # Select a few test users
        frequent_users = customer_orders_df['customer_id'].value_counts().head(5).index.tolist()
        test_users = frequent_users[:3]
        
        print(f"👥 Testing with users: {[u[:8] + '...' for u in test_users]}")
        
        results = {
            'without_deduplication': {},
            'with_deduplication': {}
        }
        
        for eval_date in test_dates:
            print(f"\n📅 Testing {eval_date.strftime('%Y-%m-%d')}...")
            
            # Filter training data
            train_data = customer_orders_df[customer_orders_df['delivery_date'] < eval_date]
            
            if len(train_data) == 0:
                continue
            
            # Build matrices
            model_data = rec_system.build_matrices_ultra_fast(train_data, eval_date)
            user_profiles = rec_system.build_user_profiles_lightning_fast(
                model_data['user_item_matrix'], 
                model_data['user_item_qty_matrix']
            )
            
            # Analyze daily patterns for deduplication
            rec_system.analyze_daily_purchase_patterns(customer_orders_df, eval_date)
            
            date_results = {
                'without_deduplication': {},
                'with_deduplication': {}
            }
            
            for user_id in test_users:
                if user_id not in user_profiles:
                    continue
                
                # Test WITHOUT deduplication
                recs_without = rec_system.get_recommendations_with_intelligent_deduplication(
                    user_id, user_profiles[user_id], model_data['similarity_dict'], 
                    eval_date, 10, enable_deduplication=False
                )
                
                # Test WITH deduplication
                recs_with = rec_system.get_recommendations_with_intelligent_deduplication(
                    user_id, user_profiles[user_id], model_data['similarity_dict'], 
                    eval_date, 10, enable_deduplication=True
                )
                
                date_results['without_deduplication'][user_id] = [r['product_name'] for r in recs_without]
                date_results['with_deduplication'][user_id] = [r['product_name'] for r in recs_with]
            
            results['without_deduplication'][eval_date] = date_results['without_deduplication']
            results['with_deduplication'][eval_date] = date_results['with_deduplication']
            
            print(f"   ✅ Generated recommendations for {len(date_results['without_deduplication'])} users")
        
        # Analyze the results
        print(f"\n📊 Analysis of Deduplication Impact:")
        print("=" * 50)
        
        if len(test_dates) >= 2:
            for user_id in test_users:
                print(f"\n👤 User: {user_id[:8]}...")
                
                # Check consecutive day similarities
                for i in range(len(test_dates) - 1):
                    date1, date2 = test_dates[i], test_dates[i + 1]
                    
                    if (user_id in results['without_deduplication'].get(date1, {}) and 
                        user_id in results['without_deduplication'].get(date2, {})):
                        
                        # Without deduplication
                        recs1_without = set(results['without_deduplication'][date1][user_id])
                        recs2_without = set(results['without_deduplication'][date2][user_id])
                        overlap_without = len(recs1_without.intersection(recs2_without))
                        similarity_without = overlap_without / len(recs1_without.union(recs2_without)) if len(recs1_without.union(recs2_without)) > 0 else 0
                        
                        # With deduplication
                        recs1_with = set(results['with_deduplication'][date1][user_id])
                        recs2_with = set(results['with_deduplication'][date2][user_id])
                        overlap_with = len(recs1_with.intersection(recs2_with))
                        similarity_with = overlap_with / len(recs1_with.union(recs2_with)) if len(recs1_with.union(recs2_with)) > 0 else 0
                        
                        print(f"   📅 {date1.strftime('%m-%d')} → {date2.strftime('%m-%d')}:")
                        print(f"      Without deduplication: {similarity_without:.1%} similarity ({overlap_without} common)")
                        print(f"      With deduplication:    {similarity_with:.1%} similarity ({overlap_with} common)")
                        
                        improvement = similarity_without - similarity_with
                        if improvement > 0:
                            print(f"      ✅ Improvement: {improvement:.1%} less repetition")
                        elif improvement < 0:
                            print(f"      ⚠️ Regression: {abs(improvement):.1%} more repetition")
                        else:
                            print(f"      ➡️ No change in repetition")
        
        # Show sample recommendations
        print(f"\n📝 Sample Recommendations Comparison:")
        print("=" * 40)
        
        if test_dates and test_users:
            sample_date = test_dates[-1]  # Last date
            sample_user = test_users[0]   # First user
            
            if (sample_user in results['without_deduplication'].get(sample_date, {}) and
                sample_user in results['with_deduplication'].get(sample_date, {})):
                
                without_recs = results['without_deduplication'][sample_date][sample_user]
                with_recs = results['with_deduplication'][sample_date][sample_user]
                
                print(f"👤 User: {sample_user[:8]}... on {sample_date.strftime('%Y-%m-%d')}")
                print(f"   Without deduplication: {without_recs[:5]}")
                print(f"   With deduplication:    {with_recs[:5]}")
                
                # Check for daily essentials
                if sample_user in rec_system.user_daily_essentials:
                    essentials = rec_system.user_daily_essentials[sample_user]
                    if essentials:
                        print(f"   Daily essentials detected: {list(essentials.keys())[:3]}")
                    else:
                        print(f"   No daily essentials detected")
                else:
                    print(f"   No daily essentials data available")
        
        print(f"\n🎉 Validation completed successfully!")
        print(f"💡 The deduplication system helps reduce repetitive recommendations while preserving daily essentials.")
        
    except Exception as e:
        print(f"❌ Error during validation: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    validate_deduplication_system()
