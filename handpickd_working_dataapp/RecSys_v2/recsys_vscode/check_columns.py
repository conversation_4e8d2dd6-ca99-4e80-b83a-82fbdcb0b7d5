#!/usr/bin/env python3
"""Check column names in data files"""

import pandas as pd

print("🔍 CHECKING COLUMN NAMES IN DATA FILES")
print("="*50)

# Check customer orders
try:
    orders_df = pd.read_csv('customer_orders.csv')
    print(f"\n📊 customer_orders.csv columns:")
    print(f"   Shape: {orders_df.shape}")
    print(f"   Columns: {list(orders_df.columns)}")
    print(f"   Sample data:")
    print(orders_df.head(2))
except Exception as e:
    print(f"❌ Error loading customer_orders.csv: {e}")

# Check catalogue
try:
    catalogue_df = pd.read_csv('catalogue_rc.csv')
    print(f"\n📊 catalogue_rc.csv columns:")
    print(f"   Shape: {catalogue_df.shape}")
    print(f"   Columns: {list(catalogue_df.columns)}")
    print(f"   Sample data:")
    print(catalogue_df.head(2))
except Exception as e:
    print(f"❌ Error loading catalogue_rc.csv: {e}")

# Check repurchase ratios
try:
    repurchase_df = pd.read_csv('repurchase_ratios.csv')
    print(f"\n📊 repurchase_ratios.csv columns:")
    print(f"   Shape: {repurchase_df.shape}")
    print(f"   Columns: {list(repurchase_df.columns)}")
    print(f"   Sample data:")
    print(repurchase_df.head(2))
except Exception as e:
    print(f"❌ Error loading repurchase_ratios.csv: {e}")
